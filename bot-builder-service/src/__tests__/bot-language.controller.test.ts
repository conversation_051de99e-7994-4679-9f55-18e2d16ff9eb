import { Request, Response } from "express";
import { BotLanguageController } from "../controllers/bot-language.controller";
import { Models } from "@neuratalk/bot-store";
import { AppContext } from "../types/context.types";

jest.mock("@neuratalk/common", () => ({
  getPaginatedResults: jest.fn(),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  successResponse: jest.fn((data) => ({ success: true, data, timestamp: new Date() })),
  errorResponse: jest.fn((error) => ({
    success: false,
    error: { code: error.code, message: error.message },
    timestamp: new Date(),
  })),
}));

const { getPaginatedResults, successResponse, errorResponse } = require("@neuratalk/common");

describe("BotLanguageController", () => {
  let controller: BotLanguageController;
  let mockModels: Models;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockContext: AppContext;

  beforeEach(() => {
    mockModels = {
      BotLanguage: {
        create: jest.fn(),
        update: jest.fn(),
        findOne: jest.fn(),
        destroy: jest.fn(),
        bulkCreate: jest.fn(), // Add this line
      },
      Bot: {},
      Language: {},
      FaqCategory: {},
      FaqItems: {},
      FaqTranslation: {},
      Flow: {},
      IntentItems: {},
      IntentUtterance: {},
      UtteranceTranslation: {},
      Entities: {},
      BotModel: {},
    } as unknown as Models;

    mockContext = {
      db: {
        models: mockModels,
        getSequelize: jest.fn(),
        connect: jest.fn(),
        disconnect: jest.fn(),
        transaction: jest.fn((cb) => cb({} as any)), // Mock transaction to execute callback
        sequelize: { transaction: jest.fn((cb) => cb({} as any)) } as any, // Mock sequelize.transaction
        healthCheck: jest.fn(),
      } as any,
      botService: {} as any,
      flowService: {} as any,
      buildService: {} as any,
      buildSseService: {} as any,
    };

    controller = new BotLanguageController(mockContext);

    mockReq = {
      body: {},
      params: {},
      query: {},
      user: { id: "user-123" },
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should assign language to bot successfully", async () => {
      const mockBotLanguage = {
        id: "bot-lang-123",
        botId: "bot-1",
        langId: "lang-1",
        isDefault: false, // Default to false as per new logic
      };
      mockReq.params = { botId: "bot-1" }; // botId from params
      mockReq.body = { langId: "lang-1" }; // No isDefault in body
      (mockModels.BotLanguage.create as jest.Mock).mockResolvedValue(mockBotLanguage);

      await controller.create(mockReq as any, mockRes as Response);

      // No update call expected for isDefault
      expect(mockModels.BotLanguage.create).toHaveBeenCalledWith({
        botId: "bot-1",
        langId: "lang-1",
        isDefault: false, // Expect isDefault to be false
        createdBy: "user-123",
      });
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(successResponse).toHaveBeenCalledWith(mockBotLanguage);
    });

    it("should handle creation error", async () => {
      mockReq.body = { botId: "bot-1", langId: "lang-1" };
      (mockModels.BotLanguage.create as jest.Mock).mockRejectedValue(new Error("Creation failed"));

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "VALIDATION_ERROR",
      });
    });
  });

  describe("bulkCreate", () => {
    it("should bulk create bot languages successfully", async () => {
      const mockBotLanguagesPayload = {
        ids: ["lang-1", "lang-2"],
      };
      const createdLanguages = [
        { id: "bl-1", botId: "bot-1", langId: "lang-1", isDefault: false },
        { id: "bl-2", botId: "bot-1", langId: "lang-2", isDefault: false },
      ];
      mockReq.params = { botId: "bot-1" };
      mockReq.body = mockBotLanguagesPayload;

      (mockModels.BotLanguage.bulkCreate as jest.Mock).mockResolvedValue(createdLanguages);

      await controller.bulkCreate(mockReq as any, mockRes as Response);

      expect(mockContext.db.sequelize.transaction).toHaveBeenCalled();
      expect(mockModels.BotLanguage.bulkCreate).toHaveBeenCalledWith(
        [
          { botId: "bot-1", langId: "lang-1", createdBy: "user-123", isDefault: false },
          { botId: "bot-1", langId: "lang-2", createdBy: "user-123", isDefault: false },
        ],
        { transaction: expect.any(Object) },
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(successResponse).toHaveBeenCalledWith(createdLanguages);
    });

    it("should handle bulk creation error", async () => {
      mockReq.body = [{ botId: "bot-1", langId: "lang-1" }];

      await controller.bulkCreate(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "VALIDATION_ERROR",
      });
    });
  });

  describe("bulkDelete", () => {
    beforeEach(() => {
      mockReq.params = { botId: "bot-1" };
    });

    it("should bulk delete bot languages successfully", async () => {
      mockReq.body = { ids: ["bl-1", "bl-2"] };
      (mockModels.BotLanguage.destroy as jest.Mock).mockResolvedValue(2);

      await controller.bulkDelete(mockReq as any, mockRes as Response);

      expect(mockContext.db.sequelize.transaction).toHaveBeenCalled();
      expect(mockModels.BotLanguage.destroy).toHaveBeenCalledWith({
        where: { id: ["bl-1", "bl-2"], botId: "bot-1", isDefault: false },
        transaction: expect.any(Object),
      });
      expect(mockRes.status).toHaveBeenCalledWith(204);
      expect(mockRes.send).toHaveBeenCalled();
    });

    it("should return 404 if no bot languages are found for deletion", async () => {
      mockReq.body = { ids: ["non-existent"] };
      (mockModels.BotLanguage.destroy as jest.Mock).mockResolvedValue(0);

      await controller.bulkDelete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "No bot languages found for deletion or attempting to delete default language",
      });
    });

    it("should handle bulk delete error", async () => {
      mockReq.body = { ids: ["bl-1"] };
      (mockModels.BotLanguage.destroy as jest.Mock).mockRejectedValue(new Error("Bulk deletion failed"));

      await controller.bulkDelete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "INTERNAL_ERROR",
        message: "Failed to bulk unassign bot languages",
      });
    });
  });

  describe("getAll", () => {
    it("should retrieve all bot languages", async () => {
      const mockResult = {
        items: [{ id: "bot-lang-1" }],
        pagination: { page: 1, limit: 20, total: 1, totalPages: 1, hasNext: false, hasPrev: false },
      };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockResult);

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(getPaginatedResults).toHaveBeenCalledWith(mockModels.BotLanguage, mockReq.query);
      expect(successResponse).toHaveBeenCalledWith(mockResult);
    });

    it("should handle getAll error", async () => {
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "INTERNAL_ERROR",
        message: "Failed to fetch bot languages",
      });
    });
  });

  describe("getById", () => {
    it("should retrieve bot language by ID", async () => {
      const mockBotLanguage = { id: "bot-lang-123" };
      mockReq.params = { id: "bot-lang-123" };
      (mockModels.BotLanguage.findOne as jest.Mock).mockResolvedValue(mockBotLanguage);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockModels.BotLanguage.findOne).toHaveBeenCalledWith({
        where: { id: "bot-lang-123" },
      });
      expect(successResponse).toHaveBeenCalledWith(mockBotLanguage);
    });

    it("should return 404 for non-existent bot language", async () => {
      mockReq.params = { id: "non-existent" };
      (mockModels.BotLanguage.findOne as jest.Mock).mockResolvedValue(null);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "Bot language not found",
      });
    });

    it("should handle getById error", async () => {
      mockReq.params = { id: "bot-lang-123" };
      (mockModels.BotLanguage.findOne as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "INTERNAL_ERROR",
        message: "Failed to fetch bot language",
      });
    });
  });

  describe("delete", () => {
    it("should delete bot language successfully", async () => {
      mockReq.params = { id: "bot-lang-123" };
      (mockModels.BotLanguage.destroy as jest.Mock).mockResolvedValue(1);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockModels.BotLanguage.destroy).toHaveBeenCalledWith({
        where: { id: "bot-lang-123", isDefault: false },
      });
      expect(mockRes.status).toHaveBeenCalledWith(204);
      expect(mockRes.send).toHaveBeenCalled();
    });

    it("should return 404 for non-existent bot language", async () => {
      mockReq.params = { id: "non-existent" };
      (mockModels.BotLanguage.destroy as jest.Mock).mockResolvedValue(0);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "Bot language not found",
      });
    });

    it("should handle delete error", async () => {
      mockReq.params = { id: "bot-lang-123" };
      (mockModels.BotLanguage.destroy as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "INTERNAL_ERROR",
        message: "Failed to unassign bot language",
      });
    });
  });
});