import { createRoutes } from "../routers/index.router";
import { AppContext } from "../types/context.types";

jest.mock("api_gw", () => ({
  initializeApiGw: jest.fn().mockResolvedValue(undefined),
  getStudioAppsService: jest.fn().mockReturnValue({
    createAppInfo: jest.fn(),
    updateAppInfo: jest.fn(),
    getAllApps: jest.fn(),
    getAppInfo: jest.fn(),
    purgeAppInfo: jest.fn(),
    check4Blacklist: jest.fn((req, res, next) => next()),
    validateCreateAppInfo: jest.fn((req, res, next) => next()),
    checkForDuplicateName: jest.fn((req, res, next) => next()),
  }),
}));

describe("Route Initialization", () => {
  it("should initialize all routes without errors", () => {
    // Mock AppContext as it's required by createRoutes
    const mockAppContext: AppContext = {
      db: {} as any, // Mock DatabaseConnection
      botService: {} as any, // Mock BotService
      flowService: {} as any, // Mock FlowService
      buildService: {} as any, // Mock BuildService
      buildSseService: {} as any, // Mock BuildSseService
    };

    // Call createRoutes and expect it not to throw
    const routes = createRoutes(mockAppContext);

    // Expect routes to be an array (each element is a Router instance)
    expect(Array.isArray(routes)).toBe(true);
    expect(routes.length).toBeGreaterThan(0); // Assuming there's at least one router
  });
});
