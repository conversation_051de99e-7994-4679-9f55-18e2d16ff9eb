import { getTotalPages } from "../../utils/api";

describe("API Utils", () => {
  describe("getTotalPages", () => {
    it("should calculate total pages correctly for exact division", () => {
      expect(getTotalPages(100, 10)).toBe(10);
      expect(getTotalPages(50, 25)).toBe(2);
      expect(getTotalPages(20, 5)).toBe(4);
    });

    it("should calculate total pages correctly with remainder", () => {
      expect(getTotalPages(101, 10)).toBe(11);
      expect(getTotalPages(23, 5)).toBe(5);
      expect(getTotalPages(7, 3)).toBe(3);
    });

    it("should handle zero total items", () => {
      expect(getTotalPages(0, 10)).toBe(0);
      expect(getTotalPages(0, 1)).toBe(0);
    });

    it("should handle single item per page", () => {
      expect(getTotalPages(5, 1)).toBe(5);
      expect(getTotalPages(1, 1)).toBe(1);
    });

    it("should handle large numbers", () => {
      expect(getTotalPages(1000000, 100)).toBe(10000);
      expect(getTotalPages(999999, 100)).toBe(10000);
    });

    it("should handle edge cases", () => {
      expect(getTotalPages(1, 10)).toBe(1);
      expect(getTotalPages(9, 10)).toBe(1);
      expect(getTotalPages(10, 10)).toBe(1);
      expect(getTotalPages(11, 10)).toBe(2);
    });
  });
});
