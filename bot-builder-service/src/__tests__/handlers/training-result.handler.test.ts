import { TrainingResultHandler } from "../../handlers/training-result.handler";
import { BuildService } from "../../services/build.service";
import { logger } from "@neuratalk/common";
import { TrainingJobStatus } from "@neuratalk/bot-store";
import { EachMessagePayload } from "kafkajs";

// Mock dependencies
jest.mock("@neuratalk/common", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe("TrainingResultHandler", () => {
  let trainingResultHandler: TrainingResultHandler;
  let mockBuildService: jest.Mocked<BuildService>;

  beforeEach(() => {
    // Mock BuildService
    mockBuildService = {
      updateBuildJob: jest.fn(),
      createBuild: jest.fn(),
    } as any;

    // Create handler instance
    trainingResultHandler = new TrainingResultHandler(mockBuildService);

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe("handleMessage", () => {
    it("should successfully process a completed training result message", async () => {
      // Arrange
      const trainingMessage = {
        jobId: "test-job-id",
        botId: "test-bot-id",
        status: TrainingJobStatus.COMPLETED,
        modelUrl: "https://example.com/model.zip",
      };

      const payload: EachMessagePayload = {
        topic: "training-results",
        partition: 0,
        message: {
          key: null,
          value: Buffer.from(JSON.stringify(trainingMessage)),
          timestamp: "1234567890",
          attributes: 0,
          offset: "0",
          headers: {},
        },
        heartbeat: jest.fn(),
        pause: jest.fn(),
      };

      mockBuildService.updateBuildJob.mockResolvedValue(undefined);

      // Act
      await trainingResultHandler.handleMessage(payload);

      // Assert
      expect(logger.info).toHaveBeenCalledWith("Received training result:", {
        message: trainingMessage,
      });

      expect(mockBuildService.updateBuildJob).toHaveBeenCalledWith(trainingMessage.jobId, {
        status: trainingMessage.status,
        modelUrl: trainingMessage.modelUrl,
        errorMessage: undefined,
      });
    });

    it("should successfully process a failed training result message", async () => {
      // Arrange
      const trainingMessage = {
        jobId: "test-job-id",
        botId: "test-bot-id",
        status: TrainingJobStatus.FAILED,
        errorMessage: "Training failed due to insufficient data",
      };

      const payload: EachMessagePayload = {
        topic: "training-results",
        partition: 0,
        message: {
          key: null,
          value: Buffer.from(JSON.stringify(trainingMessage)),
          timestamp: "1234567890",
          attributes: 0,
          offset: "0",
          headers: {},
        },
        heartbeat: jest.fn(),
        pause: jest.fn(),
      };

      mockBuildService.updateBuildJob.mockResolvedValue(undefined);

      // Act
      await trainingResultHandler.handleMessage(payload);

      // Assert
      expect(logger.info).toHaveBeenCalledWith("Received training result:", {
        message: trainingMessage,
      });

      expect(mockBuildService.updateBuildJob).toHaveBeenCalledWith(trainingMessage.jobId, {
        status: trainingMessage.status,
        modelUrl: undefined,
        errorMessage: trainingMessage.errorMessage,
      });
    });

    it("should handle invalid JSON message", async () => {
      // Arrange
      const invalidJson = "invalid json";
      const payload: EachMessagePayload = {
        topic: "training-results",
        partition: 0,
        message: {
          key: null,
          value: Buffer.from(invalidJson),
          timestamp: "1234567890",
          attributes: 0,
          offset: "0",
          headers: {},
        },
        heartbeat: jest.fn(),
        pause: jest.fn(),
      };

      // Act
      await trainingResultHandler.handleMessage(payload);

      // Assert
      expect(logger.error).toHaveBeenCalledWith("Error processing training result message:", {
        error: expect.any(SyntaxError),
        value: invalidJson,
      });

      expect(mockBuildService.updateBuildJob).not.toHaveBeenCalled();
    });

    it("should handle buildService.updateBuildJob errors", async () => {
      // Arrange
      const trainingMessage = {
        jobId: "test-job-id",
        botId: "test-bot-id",
        status: TrainingJobStatus.COMPLETED,
        modelUrl: "https://example.com/model.zip",
      };

      const payload: EachMessagePayload = {
        topic: "training-results",
        partition: 0,
        message: {
          key: null,
          value: Buffer.from(JSON.stringify(trainingMessage)),
          timestamp: "1234567890",
          attributes: 0,
          offset: "0",
          headers: {},
        },
        heartbeat: jest.fn(),
        pause: jest.fn(),
      };

      const mockError = new Error("Database update failed");
      mockBuildService.updateBuildJob.mockRejectedValue(mockError);

      // Act
      await trainingResultHandler.handleMessage(payload);

      // Assert
      expect(logger.info).toHaveBeenCalledWith("Received training result:", {
        message: trainingMessage,
      });

      expect(logger.error).toHaveBeenCalledWith("Error processing training result message:", {
        error: mockError,
        value: JSON.stringify(trainingMessage),
      });

      expect(mockBuildService.updateBuildJob).toHaveBeenCalledWith(trainingMessage.jobId, {
        status: trainingMessage.status,
        modelUrl: trainingMessage.modelUrl,
        errorMessage: undefined,
      });
    });

    it("should handle null message value", async () => {
      // Arrange
      const payload: EachMessagePayload = {
        topic: "training-results",
        partition: 0,
        message: {
          key: null,
          value: null,
          timestamp: "1234567890",
          attributes: 0,
          offset: "0",
          headers: {},
        },
        heartbeat: jest.fn(),
        pause: jest.fn(),
      };

      // Act
      await trainingResultHandler.handleMessage(payload);

      // Assert
      expect(logger.error).toHaveBeenCalledWith("Error processing training result message:", {
        error: expect.any(TypeError),
        value: undefined,
      });

      expect(mockBuildService.updateBuildJob).not.toHaveBeenCalled();
    });

    it("should handle message with all optional fields", async () => {
      // Arrange
      const trainingMessage = {
        jobId: "test-job-id",
        botId: "test-bot-id",
        status: TrainingJobStatus.COMPLETED,
        modelUrl: "https://example.com/model.zip",
        errorMessage: "Some warning message",
      };

      const payload: EachMessagePayload = {
        topic: "training-results",
        partition: 0,
        message: {
          key: null,
          value: Buffer.from(JSON.stringify(trainingMessage)),
          timestamp: "1234567890",
          attributes: 0,
          offset: "0",
          headers: {},
        },
        heartbeat: jest.fn(),
        pause: jest.fn(),
      };

      mockBuildService.updateBuildJob.mockResolvedValue(undefined);

      // Act
      await trainingResultHandler.handleMessage(payload);

      // Assert
      expect(mockBuildService.updateBuildJob).toHaveBeenCalledWith(trainingMessage.jobId, {
        status: trainingMessage.status,
        modelUrl: trainingMessage.modelUrl,
        errorMessage: trainingMessage.errorMessage,
      });
    });
  });
});
