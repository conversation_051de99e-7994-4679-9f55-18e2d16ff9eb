import { Request, Response, NextFunction } from "express";
import { FaqTranslationController } from "../controllers/faq-translation.controller";
import { validateFaqQuestions } from "../middleware/faqValidation.middleware";
import { PlatformConfigKey } from "@neuratalk/bot-store";

jest.mock("@neuratalk/common", () => ({
  getPaginatedResults: jest.fn(),
  logger: { info: jest.fn(), error: jest.fn(), warn: jest.fn(), debug: jest.fn() },
  successResponse: jest.fn((data) => ({ success: true, data })),
  errorResponse: jest.fn((error) => ({
    success: false,
    error: { code: error.code, message: error.message },
  })),
}));

const { getPaginatedResults, successResponse, errorResponse } = require("@neuratalk/common");

describe("FaqTranslationController", () => {
  let controller: FaqTranslationController;
  let mockModels: any;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockContext: any;

  beforeEach(() => {
    mockModels = {
      FaqItems: { findByPk: jest.fn(), create: jest.fn(), destroy: jest.fn(), update: jest.fn() },
      FaqTranslation: {
        create: jest.fn(),
        findOne: jest.fn(),
        findByPk: jest.fn(),
        update: jest.fn(),
        destroy: jest.fn(),
        count: jest.fn(),
        findAll: jest.fn(),
      },
      PlatformConfig: { findOne: jest.fn() }, // Mock PlatformConfig
    };

    mockContext = {
      db: { transaction: jest.fn((cb) => cb({})), models: mockModels },
    };

    controller = new FaqTranslationController(mockContext);
    mockReq = { body: {}, params: {}, query: {}, user: { id: "user-123" } };
    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };
    jest.clearAllMocks();
  });

  describe("createFaqTranslation", () => {
    it("should create FAQ item and translation successfully", async () => {
      const mockFaqItem = { id: "faq-123", toJSON: () => ({ id: "faq-123" }) };
      const mockTranslation = { id: "trans-123", toJSON: () => ({ id: "trans-123" }) };
      mockReq.body = {
        categoryId: "cat-1",
        botId: "bot-1",
        langId: "lang-1",
        questions: ["Q1"],
        answer: "A1",
      };
      mockModels.FaqItems.create.mockResolvedValue(mockFaqItem);
      mockModels.FaqTranslation.create.mockResolvedValue(mockTranslation);

      await controller.createFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(201);
    });

    it("should add translation to existing FAQ item", async () => {
      const mockFaqItem = { id: "faq-123", toJSON: () => ({ id: "faq-123" }) };
      const mockTranslation = { id: "trans-123", toJSON: () => ({ id: "trans-123" }) };
      mockReq.body = { faqId: "faq-123", langId: "lang-2", questions: ["Q2"], answer: "A2" };
      mockModels.FaqItems.findByPk.mockResolvedValue(mockFaqItem);
      mockModels.FaqTranslation.findOne.mockResolvedValue(null);
      mockModels.FaqTranslation.create.mockResolvedValue(mockTranslation);

      await controller.createFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(201);
    });

    it("should return 400 if FAQ item not found", async () => {
      mockReq.body = { faqId: "non-existent", langId: "lang-1", questions: ["Q1"], answer: "A1" };
      mockModels.FaqItems.findByPk.mockResolvedValue(null);

      await controller.createFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });

    it("should return 400 if translation already exists", async () => {
      const mockFaqItem = { id: "faq-123" };
      mockReq.body = { faqId: "faq-123", langId: "lang-1", questions: ["Q1"], answer: "A1" };
      mockModels.FaqItems.findByPk.mockResolvedValue(mockFaqItem);
      mockModels.FaqTranslation.findOne.mockResolvedValue({ id: "existing" });

      await controller.createFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });

    it("should handle creation error", async () => {
      mockReq.body = {
        categoryId: "cat-1",
        botId: "bot-1",
        langId: "lang-1",
        questions: ["Q1"],
        answer: "A1",
      };
      mockModels.FaqItems.create.mockRejectedValue(new Error("Creation failed"));

      await controller.createFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });
  });

  describe("getFaqsByCategoryAndLanguage", () => {
    it("should retrieve FAQs by category and language", async () => {
      const mockResult = {
        items: [
          {
            id: "faq-1",
            flowId: "flow-1",
            botId: "bot-1",
            categoryId: "cat-1",
            faqTranslations: [
              { toJSON: () => ({ id: "trans-1", faqId: "faq-1", langId: "lang-1" }) },
            ],
          },
        ],
      };
      mockReq.params = { categoryId: "cat-1", langId: "lang-1" };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockResult);
      mockModels.FaqTranslation.findAll.mockResolvedValue([
        { faqId: "faq-1", langId: "lang-2", language: { name: "Spanish", code: "es" } },
      ]);

      await controller.getFaqsByCategoryAndLanguage(mockReq as any, mockRes as Response);

      expect(successResponse).toHaveBeenCalled();
    });

    it("should handle error", async () => {
      mockReq.params = { categoryId: "cat-1", langId: "lang-1" };
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getFaqsByCategoryAndLanguage(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("getById", () => {
    it("should retrieve FAQ translation by ID", async () => {
      const mockTranslation = { id: "trans-123" };
      mockReq.params = { id: "trans-123" };
      mockModels.FaqTranslation.findOne.mockResolvedValue(mockTranslation);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(successResponse).toHaveBeenCalledWith(mockTranslation);
    });

    it("should return 404 for non-existent translation", async () => {
      mockReq.params = { id: "non-existent" };
      mockModels.FaqTranslation.findOne.mockResolvedValue(null);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle error", async () => {
      mockReq.params = { id: "trans-123" };
      mockModels.FaqTranslation.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("getTranslationsByFaqId", () => {
    it("should retrieve translations by FAQ ID", async () => {
      const mockResult = { items: [{ id: "trans-1" }] };
      mockReq.params = { faqId: "faq-123" };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockResult);

      await controller.getTranslationsByFaqId(mockReq as any, mockRes as Response);

      expect(successResponse).toHaveBeenCalledWith(mockResult);
    });

    it("should return 404 if no translations found", async () => {
      mockReq.params = { faqId: "non-existent" };
      (getPaginatedResults as jest.Mock).mockResolvedValue({ items: [] });

      await controller.getTranslationsByFaqId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle error", async () => {
      mockReq.params = { faqId: "faq-123" };
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getTranslationsByFaqId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("getTranslationByFaqIdAndLangId", () => {
    it("should retrieve translation by FAQ and language ID", async () => {
      const mockTranslation = { id: "trans-123" };
      mockReq.params = { faqId: "faq-1", langId: "lang-1" };
      mockModels.FaqTranslation.findOne.mockResolvedValue(mockTranslation);

      await controller.getTranslationByFaqIdAndLangId(mockReq as any, mockRes as Response);

      expect(successResponse).toHaveBeenCalledWith(mockTranslation);
    });

    it("should return 404 if no translation found", async () => {
      mockReq.params = { faqId: "faq-1", langId: "lang-non-existent" };
      mockModels.FaqTranslation.findOne.mockResolvedValue(null);

      await controller.getTranslationByFaqIdAndLangId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle error", async () => {
      mockReq.params = { faqId: "faq-1", langId: "lang-1" };
      mockModels.FaqTranslation.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getTranslationByFaqIdAndLangId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("updateFaqTranslation", () => {
    it("should update FAQ translation successfully", async () => {
      const mockTranslation = {
        id: "trans-123",
        faqId: "faq-1",
        toJSON: () => ({ id: "trans-123" }),
      };
      mockReq.params = { id: "trans-123" };
      mockReq.body = { text: "Updated Text", flowId: "flow-123" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue(mockTranslation);
      mockModels.FaqTranslation.update.mockResolvedValue([1]);
      mockModels.FaqItems.update.mockResolvedValue([1]);
      mockModels.FaqTranslation.findByPk.mockResolvedValueOnce(mockTranslation);

      await controller.updateFaqTranslation(mockReq as any, mockRes as Response);

      expect(successResponse).toHaveBeenCalled();
    });

    it("should return 404 if translation not found", async () => {
      mockReq.params = { id: "non-existent" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue(null);

      await controller.updateFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle update error", async () => {
      mockReq.params = { id: "trans-123" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue({ id: "trans-123", faqId: "faq-1" });
      mockModels.FaqTranslation.update.mockRejectedValue(new Error("Update failed"));

      await controller.updateFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
    });
  });

  describe("delete", () => {
    it("should delete translation and FAQ item if no other translations", async () => {
      const mockTranslation = { id: "trans-123", faqId: "faq-1" };
      mockReq.params = { id: "trans-123" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue(mockTranslation);
      mockModels.FaqTranslation.destroy.mockResolvedValue(1);
      mockModels.FaqTranslation.count.mockResolvedValue(0);
      mockModels.FaqItems.destroy.mockResolvedValue(1);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(204);
    });

    it("should delete only translation if other translations exist", async () => {
      const mockTranslation = { id: "trans-123", faqId: "faq-1" };
      mockReq.params = { id: "trans-123" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue(mockTranslation);
      mockModels.FaqTranslation.destroy.mockResolvedValue(1);
      mockModels.FaqTranslation.count.mockResolvedValue(1);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(204);
    });

    it("should return 404 if destroy returns 0", async () => {
      const mockTranslation = { id: "trans-123", faqId: "faq-1" };
      mockReq.params = { id: "trans-123" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue(mockTranslation);
      mockModels.FaqTranslation.destroy.mockResolvedValue(0);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should return 404 if translation not found", async () => {
      mockReq.params = { id: "non-existent" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue(null);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
    });

    it("should handle delete error", async () => {
      mockReq.params = { id: "trans-123" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue({ id: "trans-123", faqId: "faq-1" });
      mockModels.FaqTranslation.destroy.mockRejectedValue(new Error("Delete failed"));

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
    });
  });

  describe("validateFaqQuestions middleware", () => {
    let nextFunction: jest.Mock<NextFunction>;

    beforeEach(() => {
      nextFunction = jest.fn();
      mockReq = { body: { questions: ["Q1", "Q2"] } } as Partial<Request>;
      mockRes = { status: jest.fn().mockReturnThis(), json: jest.fn() };
      mockModels.PlatformConfig.findOne.mockResolvedValue({
        value: "10",
      });
    });

    it("should call next() if questions are within the limit", async () => {
      mockReq.body.questions = ["Q1", "Q2", "Q3"]; // 3 questions, limit is 10
      await validateFaqQuestions(mockContext)(
        mockReq as Request,
        mockRes as Response,
        nextFunction,
      );
      expect(nextFunction).toHaveBeenCalledTimes(1);
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it("should return 400 if questions exceed the limit", async () => {
      mockReq.body.questions = Array(11).fill("Q"); // 11 questions, limit is 10
      await validateFaqQuestions(mockContext)(
        mockReq as Request,
        mockRes as Response,
        nextFunction,
      );
      expect(nextFunction).not.toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(
        errorResponse({
          code: "VALIDATION_ERROR",
          message: "A maximum of 10 questions can be only be added to a FAQ item.",
        }),
      );
    });

    it("should return 400 if questions are missing", async () => {
      mockReq.body.questions = undefined; // Missing questions
      await validateFaqQuestions(mockContext)(
        mockReq as Request,
        mockRes as Response,
        nextFunction,
      );
      expect(nextFunction).not.toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(
        errorResponse({
          code: "VALIDATION_ERROR",
          message: "'questions' array is required.",
        }),
      );
    });

    it("should return 400 if questions is not an array", async () => {
      mockReq.body.questions = "not an array"; // Invalid type
      await validateFaqQuestions(mockContext)(
        mockReq as Request,
        mockRes as Response,
        nextFunction,
      );
      expect(nextFunction).not.toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(
        errorResponse({
          code: "VALIDATION_ERROR",
          message: "'questions' array is required.",
        }),
      );
    });

    it("should use default limit if PlatformConfig is not found", async () => {
      mockModels.PlatformConfig.findOne.mockResolvedValue(null); // No config found
      mockReq.body.questions = Array(11).fill("Q"); // 11 questions, default limit is 10
      await validateFaqQuestions(mockContext)(
        mockReq as Request,
        mockRes as Response,
        nextFunction,
      );
      expect(nextFunction).not.toHaveBeenCalled();
      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith(
        errorResponse({
          code: "VALIDATION_ERROR",
          message: "A maximum of 10 questions can be only be added to a FAQ item.",
        }),
      );
    });

    // it("should handle errors during middleware execution", async () => {
    //   const originalConsoleError = console.error;
    //   console.error = jest.fn();

    //   mockModels.PlatformConfig.findOne.mockRejectedValue(new Error("DB error"));

    //   await validateFaqQuestions(mockContext)(
    //     mockReq as Request,
    //     mockRes as Response,
    //     nextFunction,
    //   );

    //   expect(nextFunction).not.toHaveBeenCalled();
    //   expect(mockRes.status).toHaveBeenCalledWith(500);
    //   expect(mockRes.json).toHaveBeenCalledWith(
    //     errorResponse({
    //       code: "VALIDATION_ERROR",
    //       message: "DB error",
    //       error: expect.any(Error),
    //     }),
    //   );

    //   console.error = originalConsoleError;
    // });
  });
});
