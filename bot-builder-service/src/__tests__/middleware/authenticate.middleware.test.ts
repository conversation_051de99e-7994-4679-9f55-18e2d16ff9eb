import { Request, Response, NextFunction } from "express";
import { authenticateMiddleware } from "../../middleware/authenticate.middleware";
import { errorResponse } from "@neuratalk/common";
import jwt from "jsonwebtoken";
import { ADMIN_USER_ID } from "../../config";

// Mock jwt.decode to control its behavior
jest.mock("jsonwebtoken", () => ({
  decode: jest.fn(),
}));

jest.mock("@neuratalk/common", () => ({
  ...jest.requireActual("@neuratalk/common"),
  errorResponse: jest.fn((error) => ({
    success: false,
    error: { code: error.code, message: error.message },
  })),
}));

describe("authenticateMiddleware", () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: jest.Mock<NextFunction>;

  beforeEach(() => {
    mockRequest = {
      headers: {},
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    nextFunction = jest.fn();
    (jwt.decode as jest.Mock).mockClear();
  });

  it("should call next() and set req.user for a valid token", () => {
    const mockToken = "Bearer valid_token";
    const decodedPayload = {
      sub: "user123",
      name: "Test User",
      preferred_username: "testuser",
      scope: "neuratalk_c neuratalk_e",
      exp: Math.floor(Date.now() / 1000) + 3600, // Token expires in 1 hour
    };

    mockRequest.headers = { authorization: mockToken };
    (jwt.decode as jest.Mock).mockReturnValue(decodedPayload);

    authenticateMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);

    expect(jwt.decode).toHaveBeenCalledWith("valid_token");
    expect(mockRequest.user).toEqual({
      id: decodedPayload.sub,
      name: decodedPayload.name,
      username: decodedPayload.preferred_username,
      permissions: decodedPayload.scope.split(" "),
    });
    expect(nextFunction).toHaveBeenCalledTimes(1);
    expect(mockResponse.status).not.toHaveBeenCalled();
    expect(mockResponse.json).not.toHaveBeenCalled();
  });

  it("should return 401 if Authorization header is missing", () => {
    authenticateMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);

    expect(jwt.decode).not.toHaveBeenCalled();
    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith(
      errorResponse({
        code: "AUTH_ERROR",
        message: "Missing or invalid Authorization header",
      }),
    );
    expect(nextFunction).not.toHaveBeenCalled();
  });

  it("should return 401 if Authorization header is not Bearer", () => {
    mockRequest.headers = { authorization: "Basic some_token" };

    authenticateMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);

    expect(jwt.decode).not.toHaveBeenCalled();
    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith(
      errorResponse({
        code: "AUTH_ERROR",
        message: "Missing or invalid Authorization header",
      }),
    );
    expect(nextFunction).not.toHaveBeenCalled();
  });

  it("should return 401 if token is expired", () => {
    const mockToken = "Bearer expired_token";
    const decodedPayload = {
      sub: "user123",
      name: "Test User",
      preferred_username: "testuser",
      scope: "neuratalk_c neuratalk_e",
      exp: Math.floor(Date.now() / 1000) - 3600, // Token expired 1 hour ago
    };

    mockRequest.headers = { authorization: mockToken };
    (jwt.decode as jest.Mock).mockReturnValue(decodedPayload);

    authenticateMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);

    expect(jwt.decode).toHaveBeenCalledWith("expired_token");
    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith(
      errorResponse({
        code: "AUTH_ERROR",
        message: "Token of the user has expired",
      }),
    );
    expect(nextFunction).not.toHaveBeenCalled();
  });

  it("should return 401 if decoded token is null or undefined", () => {
    mockRequest.headers = { authorization: "Bearer invalid_token" };
    (jwt.decode as jest.Mock).mockReturnValue(null);

    authenticateMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);

    expect(jwt.decode).toHaveBeenCalledWith("invalid_token");
    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith(
      errorResponse({
        code: "AUTH_ERROR",
        message: "Invalid token",
      }),
    );
    expect(nextFunction).not.toHaveBeenCalled();
  });

  it("should return 500 if jwt.decode throws an error", () => {
    mockRequest.headers = { authorization: "Bearer error_token" };
    const mockError = new Error("Decoding error");
    (jwt.decode as jest.Mock).mockImplementation(() => {
      throw mockError;
    });

    authenticateMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);

    expect(jwt.decode).toHaveBeenCalledWith("error_token");
    expect(mockResponse.status).toHaveBeenCalledWith(500);
    expect(mockResponse.json).toHaveBeenCalledWith(
      errorResponse({
        code: "AUTH_ERROR",
        message: "Authentication failed",
        error: mockError,
      }),
    );
    expect(nextFunction).not.toHaveBeenCalled();
  });

  it("should use ADMIN_USER_ID if sub is missing in decoded token", () => {
    const mockToken = "Bearer token_without_sub";
    const decodedPayload = {
      name: "Admin User",
      preferred_username: "admin",
      scope: "neuratalk_c",
      exp: Math.floor(Date.now() / 1000) + 3600,
    };

    mockRequest.headers = { authorization: mockToken };
    (jwt.decode as jest.Mock).mockReturnValue(decodedPayload);

    authenticateMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);

    expect(mockRequest.user?.id).toBe(ADMIN_USER_ID);
    expect(nextFunction).toHaveBeenCalledTimes(1);
  });

  it("should handle missing scope in decoded token", () => {
    const mockToken = "Bearer token_without_scope";
    const decodedPayload = {
      sub: "user456",
      name: "User Without Scope",
      preferred_username: "nouser",
      exp: Math.floor(Date.now() / 1000) + 3600,
    };

    mockRequest.headers = { authorization: mockToken };
    (jwt.decode as jest.Mock).mockReturnValue(decodedPayload);

    authenticateMiddleware(mockRequest as Request, mockResponse as Response, nextFunction);

    expect(mockRequest.user?.permissions).toEqual([]);
    expect(nextFunction).toHaveBeenCalledTimes(1);
  });
});
