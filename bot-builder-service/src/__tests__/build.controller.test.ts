import { Request, Response } from "express";
import { BuildController } from "../controllers/build.controller";
import { BuildService } from "../services/build.service";
import { BuildSseService } from "../services/build-sse.service";
import { BotIdParam } from "../schemas";
import { logger } from "@neuratalk/common";

// Mock the logger
jest.mock("@neuratalk/common", () => ({
  logger: {
    error: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe("BuildController", () => {
  let buildController: BuildController;
  let mockBuildService: jest.Mocked<BuildService>;
  let mockBuildSseService: jest.Mocked<BuildSseService>;
  let mockRequest: Partial<Request<BotIdParam>>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    // Create mock services
    mockBuildService = {
      createBuild: jest.fn(),
      updateBuildJob: jest.fn(),
    } as any;

    mockBuildSseService = {
      addClient: jest.fn(),
      removeClient: jest.fn(),
      sendEvent: jest.fn(),
      getInstance: jest.fn(),
    } as any;

    // Create controller instance
    buildController = new BuildController(mockBuildService, mockBuildSseService);

    // Setup mock request and response
    mockRequest = {
      params: { botId: "test-bot-id" },
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe("createBuild", () => {
    it("should successfully create a build and return 202 status", async () => {
      // Arrange
      const mockJobId = "test-job-id";
      mockBuildService.createBuild.mockResolvedValue({ jobId: mockJobId });

      // Act
      await buildController.createBuild(
        mockRequest as Request<BotIdParam>,
        mockResponse as Response,
      );

      // Assert
      expect(mockBuildService.createBuild).toHaveBeenCalledWith("test-bot-id");
      expect(mockResponse.status).toHaveBeenCalledWith(202);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: {
          message: "Build job successfully queued.",
          jobId: mockJobId,
          botId: "test-bot-id",
        },
        timestamp: expect.any(Date),
      });
    });

    it("should handle errors and return 500 status", async () => {
      // Arrange
      const mockError = new Error("Build service error");
      mockBuildService.createBuild.mockRejectedValue(mockError);

      // Act
      await buildController.createBuild(
        mockRequest as Request<BotIdParam>,
        mockResponse as Response,
      );

      // Assert
      expect(mockBuildService.createBuild).toHaveBeenCalledWith("test-bot-id");
      expect(logger.error).toHaveBeenCalledWith("Error starting build process:", mockError);
      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: "Error starting build process.",
      });
    });

    it("should handle missing botId parameter", async () => {
      // Arrange
      mockRequest.params = {} as any;

      // Act
      await buildController.createBuild(
        mockRequest as Request<BotIdParam>,
        mockResponse as Response,
      );

      // Assert
      expect(mockBuildService.createBuild).toHaveBeenCalledWith(undefined);
    });
  });

  describe("streamBuildUpdates", () => {
    it("should add client to SSE service", async () => {
      // Act
      await buildController.streamBuildUpdates(
        mockRequest as Request<BotIdParam>,
        mockResponse as Response,
      );

      // Assert
      expect(mockBuildSseService.addClient).toHaveBeenCalledWith(
        "test-bot-id",
        mockRequest,
        mockResponse,
      );
    });

    it("should handle missing botId parameter", async () => {
      // Arrange
      mockRequest.params = {} as any;

      // Act
      await buildController.streamBuildUpdates(
        mockRequest as Request<BotIdParam>,
        mockResponse as Response,
      );

      // Assert
      expect(mockBuildSseService.addClient).toHaveBeenCalledWith(
        undefined,
        mockRequest,
        mockResponse,
      );
    });
  });
});
