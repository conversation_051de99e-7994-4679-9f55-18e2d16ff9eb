import { v4 as uuidv4 } from "uuid";

// Mock uuid before importing config
jest.mock("uuid", () => ({
  v4: jest.fn(),
}));

describe("Config", () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    // Save original environment
    originalEnv = { ...process.env };
    // Clear all environment variables that affect config
    delete process.env.PORT;
    delete process.env.NODE_ENV;
    delete process.env.CORS_ORIGINS;
    delete process.env.DATABASE_URL;
    delete process.env.DB_HOST;
    delete process.env.DB_PORT;
    delete process.env.DB_NAME;
    delete process.env.DB_USER;
    delete process.env.DB_PASSWORD;
    delete process.env.DB_SSL;
    delete process.env.DB_MAX_CONNECTIONS;
    delete process.env.JWT_SECRET;
    delete process.env.BCRYPT_ROUNDS;
    delete process.env.BOT_INTERACTION_SERVICE_URL;
    delete process.env.CHAT_SERVICE_URL;
    delete process.env.LOG_LEVEL;
    delete process.env.LOG_FILE;
    delete process.env.KAFKA_BROKERS;
    delete process.env.KAFKA_GROUP_ID;
    delete process.env.ADMIN_USER_ID;

    // Clear module cache to force re-evaluation
    jest.resetModules();
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
  });

  it("should use default values when environment variables are not set", async () => {
    // Mock uuid before importing the config module
    (uuidv4 as jest.Mock).mockReturnValue("mock-uuid");

    const config = (await import("../../config")).default;
    const { ADMIN_USER_ID } = await import("../../config");

    expect(config.server.port).toBe(3000);
    expect(config.server.env).toBe("development");
    expect(config.server.corsOrigins).toEqual(["http://localhost:3000"]);
    expect(config.database.url).toBe("postgresql://username:password@localhost:5432/chatbot_db");
    expect(config.database.host).toBe("localhost");
    expect(config.database.port).toBe(3306); // From .env file
    expect(config.database.name).toBe("chatbot_db2"); // From .env file
    expect(config.database.user).toBe("root"); // From .env file
    expect(config.database.password).toBe("");
    expect(config.database.ssl).toBe(false);
    expect(config.database.maxConnections).toBe(20);
    expect(config.security.jwtSecret).toBe("your-super-secret-jwt-key");
    expect(config.security.bcryptRounds).toBe(12);
    expect(config.services.botInteractionUrl).toBe("http://localhost:3001");
    expect(config.services.chatServiceUrl).toBe("http://localhost:3002");
    expect(config.logging.level).toBe("info");
    expect(config.logging.file).toBeUndefined();
    expect(config.kafka.brokers).toEqual(["localhost:9092"]);
    expect(config.kafka.groupId).toBe("bot-builder-service");
    expect(ADMIN_USER_ID).toBe("907a5759-526a-444b-8b3e-d911161d8249"); // From .env file
  });

  it("should use environment variables when they are set", async () => {
    // Set environment variables
    process.env.PORT = "4000";
    process.env.NODE_ENV = "production";
    process.env.CORS_ORIGINS = "http://example.com,http://test.com";
    process.env.DATABASE_URL = "*************************************/prod_db";
    process.env.DB_HOST = "prod-host";
    process.env.DB_PORT = "5433";
    process.env.DB_NAME = "prod_db";
    process.env.DB_USER = "prod";
    process.env.DB_PASSWORD = "secret";
    process.env.DB_SSL = "true";
    process.env.DB_MAX_CONNECTIONS = "50";
    process.env.JWT_SECRET = "prod-jwt-secret";
    process.env.BCRYPT_ROUNDS = "15";
    process.env.BOT_INTERACTION_SERVICE_URL = "http://prod-bot:3001";
    process.env.CHAT_SERVICE_URL = "http://prod-chat:3002";
    process.env.LOG_LEVEL = "debug";
    process.env.LOG_FILE = "/var/log/app.log";
    process.env.KAFKA_BROKERS = "kafka1:9092,kafka2:9092";
    process.env.KAFKA_GROUP_ID = "prod-bot-builder";
    process.env.ADMIN_USER_ID = "admin-123";

    const config = (await import("../../config")).default;
    const { ADMIN_USER_ID } = await import("../../config");

    expect(config.server.port).toBe(4000);
    expect(config.server.env).toBe("production");
    expect(config.server.corsOrigins).toEqual(["http://example.com", "http://test.com"]);
    expect(config.database.url).toBe("*************************************/prod_db");
    expect(config.database.host).toBe("prod-host");
    expect(config.database.port).toBe(5433);
    expect(config.database.name).toBe("prod_db");
    expect(config.database.user).toBe("prod");
    expect(config.database.password).toBe("secret");
    expect(config.database.ssl).toBe(true);
    expect(config.database.maxConnections).toBe(50);
    expect(config.security.jwtSecret).toBe("prod-jwt-secret");
    expect(config.security.bcryptRounds).toBe(15);
    expect(config.services.botInteractionUrl).toBe("http://prod-bot:3001");
    expect(config.services.chatServiceUrl).toBe("http://prod-chat:3002");
    expect(config.logging.level).toBe("debug");
    expect(config.logging.file).toBe("/var/log/app.log");
    expect(config.kafka.brokers).toEqual(["kafka1:9092", "kafka2:9092"]);
    expect(config.kafka.groupId).toBe("prod-bot-builder");
    expect(ADMIN_USER_ID).toBe("admin-123");
  });

  it("should handle undefined CORS_ORIGINS and KAFKA_BROKERS", async () => {
    // Explicitly set to undefined
    process.env.CORS_ORIGINS = undefined;
    process.env.KAFKA_BROKERS = undefined;

    const config = (await import("../../config")).default;

    expect(config.server.corsOrigins).toEqual(["http://localhost:3000"]);
    expect(config.kafka.brokers).toEqual(["localhost:9092"]);
  });

  it("should handle DB_SSL as false when not 'true'", async () => {
    process.env.DB_SSL = "false";

    const config = (await import("../../config")).default;

    expect(config.database.ssl).toBe(false);
  });

  it("should handle DB_SSL as false when undefined", async () => {
    delete process.env.DB_SSL;

    const config = (await import("../../config")).default;

    expect(config.database.ssl).toBe(false);
  });

  it("should handle empty CORS_ORIGINS", async () => {
    process.env.CORS_ORIGINS = "";

    const config = (await import("../../config")).default;

    expect(config.server.corsOrigins).toEqual([""]);
  });

  it("should handle empty KAFKA_BROKERS", async () => {
    process.env.KAFKA_BROKERS = "";

    const config = (await import("../../config")).default;

    expect(config.kafka.brokers).toEqual([""]);
  });

  it("should handle single CORS_ORIGINS", async () => {
    process.env.CORS_ORIGINS = "http://single-origin.com";

    const config = (await import("../../config")).default;

    expect(config.server.corsOrigins).toEqual(["http://single-origin.com"]);
  });

  it("should handle single KAFKA_BROKERS", async () => {
    process.env.KAFKA_BROKERS = "single-broker:9092";

    const config = (await import("../../config")).default;

    expect(config.kafka.brokers).toEqual(["single-broker:9092"]);
  });

  it("should handle invalid PORT as NaN", async () => {
    process.env.PORT = "invalid-port";

    const config = (await import("../../config")).default;

    expect(config.server.port).toBeNaN();
  });

  it("should handle invalid DB_PORT as NaN", async () => {
    process.env.DB_PORT = "invalid-port";

    const config = (await import("../../config")).default;

    expect(config.database.port).toBeNaN();
  });

  it("should handle invalid DB_MAX_CONNECTIONS as NaN", async () => {
    process.env.DB_MAX_CONNECTIONS = "invalid-number";

    const config = (await import("../../config")).default;

    expect(config.database.maxConnections).toBeNaN();
  });

  it("should handle invalid BCRYPT_ROUNDS as NaN", async () => {
    process.env.BCRYPT_ROUNDS = "invalid-number";

    const config = (await import("../../config")).default;

    expect(config.security.bcryptRounds).toBeNaN();
  });

  it("should use ADMIN_USER_ID from environment when provided", async () => {
    process.env.ADMIN_USER_ID = "custom-admin-id";

    const { ADMIN_USER_ID } = await import("../../config");

    expect(ADMIN_USER_ID).toBe("custom-admin-id");
  });
});
