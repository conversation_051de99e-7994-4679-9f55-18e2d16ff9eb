import { BuildSseService, BuildEvent } from "../../services/build-sse.service";
import { Request, Response } from "express";
import { logger } from "@neuratalk/common";
import { TrainingJobStatus } from "@neuratalk/bot-store";

jest.mock("@neuratalk/common", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe("BuildSseService", () => {
  let service: BuildSseService;
  let mockResponse: Partial<Response>;
  let mockRequest: Partial<Request>;

  beforeEach(() => {
    // Reset the singleton instance before each test to ensure isolation
    (BuildSseService as any).instance = undefined;
    service = BuildSseService.getInstance();

    mockResponse = {
      writeHead: jest.fn(),
      write: jest.fn(),
      end: jest.fn(),
      flush: jest.fn(),
      headersSent: false,
    };
    mockRequest = {
      on: jest.fn(),
    };
    (logger.info as jest.Mock).mockClear();
    (logger.debug as jest.Mock).mockClear();
    (logger.warn as jest.Mock).mockClear();
    (logger.error as jest.Mock).mockClear();
  });

  it("should return a singleton instance", () => {
    const instance1 = BuildSseService.getInstance();
    const instance2 = BuildSseService.getInstance();
    expect(instance1).toBe(instance2);
  });

  describe("addClient", () => {
    it("should add a new client and send initial event", () => {
      const botId = "bot1";
      service.addClient(botId, mockRequest as Request, mockResponse as Response);

      expect(mockResponse.writeHead).toHaveBeenCalledWith(200, {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      });
      expect(mockResponse.write).toHaveBeenCalledTimes(1);
      expect(logger.debug).toHaveBeenCalledWith(`SSE client added for botId ${botId}.`);
      expect(mockRequest.on).toHaveBeenCalledWith("close", expect.any(Function));
    });

    it("should replace an existing client and close old connection", () => {
      const botId = "bot1";
      const oldMockResponse: Partial<Response> = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn(),
        flush: jest.fn(),
        headersSent: false,
      };
      service.addClient(botId, mockRequest as Request, oldMockResponse as Response);
      service.addClient(botId, mockRequest as Request, mockResponse as Response);

      expect(logger.warn).toHaveBeenCalledWith(
        `Existing SSE client for botId ${botId} will be replaced.`,
      );
      expect(oldMockResponse.end).toHaveBeenCalledTimes(1);
    });

    it("should not close old connection if headers already sent", () => {
      const botId = "bot1";
      const oldMockResponse: Partial<Response> = {
        writeHead: jest.fn(),
        write: jest.fn(),
        end: jest.fn(),
        flush: jest.fn(),
        headersSent: true,
      };
      service.addClient(botId, mockRequest as Request, oldMockResponse as Response);
      service.addClient(botId, mockRequest as Request, mockResponse as Response);

      expect(oldMockResponse.end).not.toHaveBeenCalled();
    });
  });

  describe("removeClient", () => {
    it("should remove a client", () => {
      const botId = "bot1";
      service.addClient(botId, mockRequest as Request, mockResponse as Response);
      (mockResponse.write as jest.Mock).mockClear(); // Clear initial write from addClient
      (logger.debug as jest.Mock).mockClear(); // Clear initial debug logs from addClient

      service.removeClient(botId);

      expect(logger.debug).toHaveBeenCalledWith(`SSE client for botId ${botId} disconnected.`);
      // Verify that sending an event after removal does not write to response
      service.sendEvent(botId, {
        botId,
        jobId: "job1",
        status: TrainingJobStatus.COMPLETED,
        timestamp: "now",
      });
      expect(mockResponse.write).not.toHaveBeenCalled();
      expect(logger.debug).toHaveBeenCalledWith(
        `No active SSE client for botId ${botId} to send event.`,
      );
    });
  });

  describe("sendEvent", () => {
    it("should send an event to an active client", () => {
      const botId = "bot1";
      service.addClient(botId, mockRequest as Request, mockResponse as Response);

      // Clear both write and flush mocks after addClient
      (mockResponse.write as jest.Mock).mockClear();
      (mockResponse.flush as jest.Mock).mockClear();

      const event: BuildEvent = {
        botId,
        jobId: "job1",
        status: TrainingJobStatus.COMPLETED,
        timestamp: "now",
      };
      service.sendEvent(botId, event);

      expect(mockResponse.write).toHaveBeenCalledTimes(1);
      expect(mockResponse.write).toHaveBeenCalledWith(`data: ${JSON.stringify(event)}\n`);
      expect(mockResponse.flush).toHaveBeenCalledTimes(1);
    });

    it("should not send an event if client is not found", () => {
      const botId = "bot_non_existent";
      const event: BuildEvent = {
        botId,
        jobId: "job1",
        status: TrainingJobStatus.COMPLETED,
        timestamp: "now",
      };
      service.sendEvent(botId, event);

      expect(mockResponse.write).not.toHaveBeenCalled();
      expect(logger.debug).toHaveBeenCalledWith(
        `No active SSE client for botId ${botId} to send event.`,
      );
    });

    it("should not send an event if headers already sent and remove client", () => {
      const botId = "bot1";
      mockResponse.headersSent = true;
      service.addClient(botId, mockRequest as Request, mockResponse as Response);
      const event: BuildEvent = {
        botId,
        jobId: "job1",
        status: TrainingJobStatus.COMPLETED,
        timestamp: "now",
      };
      service.sendEvent(botId, event);

      expect(mockResponse.write).not.toHaveBeenCalled();
      expect(logger.warn).toHaveBeenCalledWith(
        `Attempted to send SSE event to botId ${botId} but headers already sent. Client might be disconnected.`,
      );
      // Expect client to be removed
      service.sendEvent(botId, event); // Second call should log no active client
      expect(logger.debug).toHaveBeenCalledWith(
        `No active SSE client for botId ${botId} to send event.`,
      );
    });

    it("should log error and remove client if write fails", () => {
      const botId = "bot1";
      service.addClient(botId, mockRequest as Request, mockResponse as Response);
      const mockError = new Error("Write failed");
      (mockResponse.write as jest.Mock).mockImplementation(() => {
        throw mockError;
      });
      const event: BuildEvent = {
        botId,
        jobId: "job1",
        status: TrainingJobStatus.COMPLETED,
        timestamp: "now",
      };
      service.sendEvent(botId, event);

      expect(logger.error).toHaveBeenCalledWith(
        `Error sending SSE event to botId ${botId}:`,
        mockError,
      );
      // Expect client to be removed
      service.sendEvent(botId, event); // Second call should log no active client
      expect(logger.debug).toHaveBeenCalledWith(
        `No active SSE client for botId ${botId} to send event.`,
      );
    });
  });
});
