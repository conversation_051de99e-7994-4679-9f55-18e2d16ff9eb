import { BuildService } from "../../services/build.service";
import { DatabaseConnection, Models, TrainingJobStatus } from "@neuratalk/bot-store";
import { logger, KafkaProducer } from "@neuratalk/common";
import { BuildSseService } from "../../services/build-sse.service";
import { v4 as uuidv4 } from "uuid";

// Mock dependencies
jest.mock("uuid");
jest.mock("@neuratalk/common", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  KafkaProducer: jest.fn(),
}));

describe("BuildService", () => {
  let buildService: BuildService;
  let mockDb: jest.Mocked<DatabaseConnection>;
  let mockKafkaProducer: jest.Mocked<KafkaProducer>;
  let mockBuildSseService: jest.Mocked<BuildSseService>;
  let mockModels: jest.Mocked<Models>;
  let mockTrainingJob: any;

  beforeEach(() => {
    // Mock TrainingJob model
    mockTrainingJob = {
      create: jest.fn(),
      findByPk: jest.fn(),
      save: jest.fn(),
    };

    // Mock Models
    mockModels = {
      TrainingJob: mockTrainingJob,
    } as any;

    // Mock DatabaseConnection
    mockDb = {
      models: mockModels,
    } as any;

    // Mock KafkaProducer
    mockKafkaProducer = {
      sendMessage: jest.fn(),
    } as any;

    // Mock BuildSseService
    mockBuildSseService = {
      sendEvent: jest.fn(),
      addClient: jest.fn(),
      removeClient: jest.fn(),
      getInstance: jest.fn(),
    } as any;

    // Create service instance
    buildService = new BuildService(mockDb, mockKafkaProducer, mockBuildSseService);

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe("createBuild", () => {
    it("should successfully create a build job", async () => {
      // Arrange
      const botId = "test-bot-id";
      const mockJobId = "test-job-id";
      (uuidv4 as jest.Mock).mockReturnValue(mockJobId);

      const mockCreatedJob = {
        id: mockJobId,
        botId,
        status: TrainingJobStatus.PENDING,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      };

      mockTrainingJob.create.mockResolvedValue(mockCreatedJob);
      mockKafkaProducer.sendMessage.mockResolvedValue(undefined);

      // Act
      const result = await buildService.createBuild(botId);

      // Assert
      expect(result).toEqual({ jobId: mockJobId });
      expect(mockTrainingJob.create).toHaveBeenCalledWith({
        id: mockJobId,
        botId,
        status: TrainingJobStatus.PENDING,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });

      expect(mockKafkaProducer.sendMessage).toHaveBeenCalledWith({
        topic: "training-requests",
        messages: [{ value: JSON.stringify({ botId, jobId: mockJobId }) }],
      });

      expect(logger.info).toHaveBeenCalledWith(
        `Build job ${mockJobId} created for bot ${botId} and queued via Kafka.`,
      );

      expect(mockBuildSseService.sendEvent).toHaveBeenCalledWith(botId, {
        botId,
        jobId: mockJobId,
        status: TrainingJobStatus.PENDING,
        timestamp: expect.any(String),
        message: `Build job ${mockJobId} queued.`,
      });
    });

    it("should handle database creation errors", async () => {
      // Arrange
      const botId = "test-bot-id";
      const mockJobId = "test-job-id";
      const mockError = new Error("Database error");
      (uuidv4 as jest.Mock).mockReturnValue(mockJobId);

      mockTrainingJob.create.mockRejectedValue(mockError);

      // Act & Assert
      await expect(buildService.createBuild(botId)).rejects.toThrow("Database error");
      expect(mockKafkaProducer.sendMessage).not.toHaveBeenCalled();
      expect(mockBuildSseService.sendEvent).not.toHaveBeenCalled();
    });

    it("should handle Kafka producer errors", async () => {
      // Arrange
      const botId = "test-bot-id";
      const mockJobId = "test-job-id";
      const mockError = new Error("Kafka error");
      (uuidv4 as jest.Mock).mockReturnValue(mockJobId);

      mockTrainingJob.create.mockResolvedValue({});
      mockKafkaProducer.sendMessage.mockRejectedValue(mockError);

      // Act & Assert
      await expect(buildService.createBuild(botId)).rejects.toThrow("Kafka error");
      expect(mockBuildSseService.sendEvent).not.toHaveBeenCalled();
    });
  });

  describe("updateBuildJob", () => {
    it("should successfully update a completed job with model URL", async () => {
      // Arrange
      const jobId = "test-job-id";
      const botId = "test-bot-id";
      const modelUrl = "https://example.com/model.zip";
      const result = {
        status: TrainingJobStatus.COMPLETED,
        modelUrl,
      };

      const mockJob = {
        id: jobId,
        botId,
        status: TrainingJobStatus.PENDING,
        completedAt: undefined as any,
        modelUrl: undefined as any,
        errorMessage: undefined as any,
        save: jest.fn().mockResolvedValue(undefined),
      };

      mockTrainingJob.findByPk.mockResolvedValue(mockJob);

      // Act
      await buildService.updateBuildJob(jobId, result);

      // Assert
      expect(mockTrainingJob.findByPk).toHaveBeenCalledWith(jobId);
      expect(mockJob.status).toBe(TrainingJobStatus.COMPLETED);
      expect(mockJob.completedAt).toBeInstanceOf(Date);
      expect(mockJob.modelUrl).toBe(modelUrl);
      expect(mockJob.save).toHaveBeenCalled();

      expect(logger.info).toHaveBeenCalledWith(
        `Updating training job ${jobId} with status ${result.status}`,
      );
      expect(logger.info).toHaveBeenCalledWith(`Successfully updated job ${jobId}.`);

      expect(mockBuildSseService.sendEvent).toHaveBeenCalledWith(botId, {
        botId,
        jobId,
        status: TrainingJobStatus.COMPLETED,
        timestamp: expect.any(String),
        message: `Build job ${jobId} status: ${TrainingJobStatus.COMPLETED}.`,
        modelUrl,
        errorMessage: undefined,
      });
    });

    it("should successfully update a failed job with error message", async () => {
      // Arrange
      const jobId = "test-job-id";
      const botId = "test-bot-id";
      const errorMessage = "Training failed due to insufficient data";
      const result = {
        status: TrainingJobStatus.FAILED,
        errorMessage,
      };

      const mockJob = {
        id: jobId,
        botId,
        status: TrainingJobStatus.PENDING,
        completedAt: undefined as any,
        modelUrl: undefined as any,
        errorMessage: undefined as any,
        save: jest.fn().mockResolvedValue(undefined),
      };

      mockTrainingJob.findByPk.mockResolvedValue(mockJob);

      // Act
      await buildService.updateBuildJob(jobId, result);

      // Assert
      expect(mockJob.status).toBe(TrainingJobStatus.FAILED);
      expect(mockJob.completedAt).toBeInstanceOf(Date);
      expect(mockJob.errorMessage).toBe(errorMessage);
      expect(mockJob.save).toHaveBeenCalled();

      expect(mockBuildSseService.sendEvent).toHaveBeenCalledWith(botId, {
        botId,
        jobId,
        status: TrainingJobStatus.FAILED,
        timestamp: expect.any(String),
        message: `Build job ${jobId} status: ${TrainingJobStatus.FAILED}.`,
        modelUrl: undefined,
        errorMessage,
      });
    });

    it("should handle job not found", async () => {
      // Arrange
      const jobId = "non-existent-job-id";
      const result = {
        status: TrainingJobStatus.COMPLETED,
      };

      mockTrainingJob.findByPk.mockResolvedValue(null);

      // Act
      await buildService.updateBuildJob(jobId, result);

      // Assert
      expect(mockTrainingJob.findByPk).toHaveBeenCalledWith(jobId);
      expect(logger.error).toHaveBeenCalledWith(`Training job with ID ${jobId} not found.`);
      expect(mockBuildSseService.sendEvent).not.toHaveBeenCalled();
    });

    it("should handle database save errors", async () => {
      // Arrange
      const jobId = "test-job-id";
      const botId = "test-bot-id";
      const result = {
        status: TrainingJobStatus.COMPLETED,
      };
      const mockError = new Error("Database save error");

      const mockJob = {
        id: jobId,
        botId,
        status: TrainingJobStatus.PENDING,
        completedAt: undefined as any,
        modelUrl: undefined as any,
        errorMessage: undefined as any,
        save: jest.fn().mockRejectedValue(mockError),
      };

      mockTrainingJob.findByPk.mockResolvedValue(mockJob);

      // Act
      await buildService.updateBuildJob(jobId, result);

      // Assert
      expect(logger.error).toHaveBeenCalledWith(
        `Failed to update job ${jobId} in database:`,
        mockError,
      );
      expect(mockBuildSseService.sendEvent).not.toHaveBeenCalled();
    });

    it("should handle findByPk errors", async () => {
      // Arrange
      const jobId = "test-job-id";
      const result = {
        status: TrainingJobStatus.COMPLETED,
      };
      const mockError = new Error("Database find error");

      mockTrainingJob.findByPk.mockRejectedValue(mockError);

      // Act
      await buildService.updateBuildJob(jobId, result);

      // Assert
      expect(logger.error).toHaveBeenCalledWith(
        `Failed to update job ${jobId} in database:`,
        mockError,
      );
      expect(mockBuildSseService.sendEvent).not.toHaveBeenCalled();
    });
  });
});
