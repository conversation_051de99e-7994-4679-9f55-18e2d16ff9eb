import { BotService } from "../../services/bot.service";
import {
  DatabaseConnection,
  Models,
  BotStatus,
  FaqCategoryType,
  FlowType,
} from "@neuratalk/bot-store";
import { FlowService } from "../../services/flow.service";
import { getStudioAppsService } from "api_gw";
import { logger } from "@neuratalk/common";
import { v4 as uuidv4 } from "uuid";

// Mock external dependencies
jest.mock("../../services/flow.service");
jest.mock("api_gw");
jest.mock("@neuratalk/common", () => ({
  ...jest.requireActual("@neuratalk/common"),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  getPaginatedResults: jest.fn(),
}));
jest.mock("uuid", () => ({
  v4: jest.fn(),
}));

const mockBotId = "bot-123";
const mockLanguageId = "lang-123";
const mockFaqCategoryId = "faq-cat-123";
const mockFlowId = "flow-123";

describe("BotService", () => {
  let botService: BotService;
  let mockDb: jest.Mocked<DatabaseConnection>;
  let mockFlowService: jest.Mocked<FlowService>;
  let mockModels: jest.Mocked<Models>;
  let mockStudioAppsService: any;

  beforeEach(() => {
    mockModels = {
      Bot: {
        create: jest.fn(),
        findByPk: jest.fn(),
        destroy: jest.fn(),
        findAndCountAll: jest.fn(),
      },
      BotLanguage: { bulkCreate: jest.fn() },
      Language: { findAll: jest.fn() },
      FaqCategory: { create: jest.fn() },
      Flow: { create: jest.fn() },
      TrainingJob: { findByPk: jest.fn() },
      IntentItems: { create: jest.fn() },
      Entities: { bulkCreate: jest.fn() },
      IntentUtterance: { create: jest.fn() },
      UtteranceTranslation: { bulkCreate: jest.fn() },
      FaqItems: { create: jest.fn() },
      FaqTranslation: { bulkCreate: jest.fn() },
    } as unknown as jest.Mocked<Models>;

    mockDb = {
      models: mockModels,
      transaction: jest.fn((callback) => callback({})) as any, // Re-add this mock
      sequelize: {
        transaction: jest.fn(() => ({
          commit: jest.fn().mockResolvedValue(undefined),
          rollback: jest.fn().mockResolvedValue(undefined),
        })),
      } as any,
    } as unknown as jest.Mocked<DatabaseConnection>;

    mockFlowService = new FlowService(mockDb) as jest.Mocked<FlowService>;
    mockFlowService.createFlow = jest.fn();

    mockStudioAppsService = {
      createApp: jest.fn(),
      exportApp: jest.fn(),
      bulkPublishApps: jest.fn(),
    };
    (getStudioAppsService as jest.Mock).mockReturnValue(mockStudioAppsService);

    (uuidv4 as jest.Mock).mockReturnValue("mock-uuid");

    botService = new BotService(mockFlowService, mockDb);

    jest.clearAllMocks();
  });

  describe("createBot", () => {
    const mockRequest = {
      name: "Test Bot",
      description: "A bot for testing",
      settings: { nlu: { provider: "dialogflow" } },
      metadata: { author: "Test User" },
    };
    const mockUserId = "user-123";
    const mockBotId = "bot-123";
    const mockLanguageId = "lang-123";
    const mockFaqCategoryId = "faq-cat-123";
    const mockFlowId = "flow-123";

    it("should create a bot with default settings and associated data", async () => {
      (mockModels.Bot.create as jest.Mock).mockResolvedValue({
        id: mockBotId,
        ...mockRequest,
        status: BotStatus.DRAFT,
        createdBy: mockUserId,
        updatedBy: mockUserId,
        toJSON: () => ({ id: mockBotId, ...mockRequest, status: BotStatus.DRAFT }),
      });
      (mockModels.Language.findAll as jest.Mock).mockResolvedValue([
        { id: mockLanguageId, code: "en", toJSON: () => ({ id: mockLanguageId, code: "en" }) },
      ]);
      (mockModels.BotLanguage.bulkCreate as jest.Mock).mockResolvedValue([]);
      (mockModels.FaqCategory.create as jest.Mock).mockResolvedValue({
        id: mockFaqCategoryId,
        toJSON: () => ({ id: mockFaqCategoryId }),
      });
      (mockFlowService.createFlow as jest.Mock).mockResolvedValue({
        id: mockFlowId,
        toJSON: () => ({ id: mockFlowId }),
      });

      const result = await botService.createBot(mockRequest, mockUserId);

      expect(mockDb.transaction).toHaveBeenCalled();
      expect(mockModels.Bot.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: mockRequest.name,
          description: mockRequest.description,
          status: BotStatus.DRAFT,
          createdBy: mockUserId,
          updatedBy: mockUserId,
        }),
        expect.any(Object),
      );
      expect(mockModels.Language.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { code: ["en"] },
        }),
      );
      expect(mockModels.BotLanguage.bulkCreate).toHaveBeenCalledWith(
        [
          {
            botId: mockBotId,
            langId: mockLanguageId,
            createdBy: mockUserId,
            isDefault: true,
          },
        ],
        expect.any(Object),
      );
      expect(mockModels.FaqCategory.create).toHaveBeenCalledWith(
        expect.objectContaining({
          botId: mockBotId,
          name: "Default",
          type: FaqCategoryType.DEFAULT,
          createdBy: mockUserId,
          updatedBy: mockUserId,
        }),
        expect.any(Object),
      );
      expect(mockFlowService.createFlow).toHaveBeenCalledTimes(2); // For Welcome and Fallback flows
      expect(mockFlowService.createFlow).toHaveBeenCalledWith(
        expect.objectContaining({
          botId: mockBotId,
          type: FlowType.DEFAULT,
        }),
        mockUserId,
        expect.any(Object),
      );
      expect(result).toEqual({ id: mockBotId, ...mockRequest, status: BotStatus.DRAFT });
    });

    it("should handle errors during bot creation", async () => {
      const error = new Error("Database error");
      (mockModels.Bot.create as jest.Mock).mockRejectedValue(error);

      await expect(botService.createBot(mockRequest, mockUserId)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith("Error creating bot:", error);
    });
  });

  describe("getBotById", () => {
    it("should retrieve a bot by ID", async () => {
      const mockBot = {
        id: mockBotId,
        name: "Test Bot",
        toJSON: () => ({ id: mockBotId, name: "Test Bot" }),
      };
      (mockModels.Bot.findByPk as jest.Mock).mockResolvedValue(mockBot);

      const result = await botService.getBotById(mockBotId);

      expect(mockModels.Bot.findByPk).toHaveBeenCalledWith(mockBotId);
      expect(result).toEqual(mockBot.toJSON());
    });

    it("should return null if bot not found", async () => {
      (mockModels.Bot.findByPk as jest.Mock).mockResolvedValue(null);

      const result = await botService.getBotById("non-existent-id");

      expect(mockModels.Bot.findByPk).toHaveBeenCalledWith("non-existent-id");
      expect(result).toBeNull();
    });

    it("should handle errors during retrieval", async () => {
      const error = new Error("Database error");
      (mockModels.Bot.findByPk as jest.Mock).mockRejectedValue(error);

      await expect(botService.getBotById(mockBotId)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(`Error getting bot ${mockBotId}:`, error);
    });
  });

  describe("updateBot", () => {
    const mockUpdateData = { name: "Updated Bot Name", description: "Updated description" };
    const mockUserId = "user-123";

    it("should update bot properties successfully", async () => {
      const existingBot = {
        id: mockBotId,
        name: "Original Name",
        description: "Original description",
        settings: { nlu: { provider: "rasa" } },
        metadata: { version: "1.0" },
        update: jest.fn().mockResolvedValue(true),
        toJSON: () => ({ id: mockBotId, ...mockUpdateData }),
      };
      (mockModels.Bot.findByPk as jest.Mock).mockResolvedValue(existingBot);

      const result = await botService.updateBot(mockBotId, mockUpdateData, mockUserId);

      expect(mockModels.Bot.findByPk).toHaveBeenCalledWith(mockBotId);
      expect(existingBot.update).toHaveBeenCalledWith(
        expect.objectContaining({
          name: mockUpdateData.name,
          description: mockUpdateData.description,
          updatedBy: mockUserId,
        }),
      );
      expect(result).toEqual(existingBot.toJSON());
    });

    it("should return null if bot not found during update", async () => {
      (mockModels.Bot.findByPk as jest.Mock).mockResolvedValue(null);

      const result = await botService.updateBot("non-existent-id", mockUpdateData, mockUserId);

      expect(mockModels.Bot.findByPk).toHaveBeenCalledWith("non-existent-id");
      expect(result).toBeNull();
    });

    it("should return existing bot if no changes are provided", async () => {
      const existingBot = {
        id: mockBotId,
        name: "Original Name",
        description: "Original description",
        settings: { nlu: { provider: "rasa" } },
        metadata: { version: "1.0" },
        update: jest.fn(),
        toJSON: () => ({
          id: mockBotId,
          name: "Original Name",
          description: "Original description",
        }),
      };
      (mockModels.Bot.findByPk as jest.Mock).mockResolvedValue(existingBot);

      const result = await botService.updateBot(mockBotId, {}, mockUserId);

      expect(mockModels.Bot.findByPk).toHaveBeenCalledWith(mockBotId);
      expect(existingBot.update).toHaveBeenCalledWith({ updatedBy: mockUserId });
      expect(result).toEqual(existingBot.toJSON());
    });

    it("should handle errors during update", async () => {
      const error = new Error("Database error");
      (mockModels.Bot.findByPk as jest.Mock).mockRejectedValue(error);

      await expect(botService.updateBot(mockBotId, mockUpdateData, mockUserId)).rejects.toThrow(
        error,
      );
      expect(logger.error).toHaveBeenCalledWith(`Error updating bot ${mockBotId}:`, error);
    });
  });

  describe("deleteBot", () => {
    it("should delete a bot successfully", async () => {
      (mockModels.Bot.destroy as jest.Mock).mockResolvedValue(1);

      const result = await botService.deleteBot(mockBotId);

      expect(mockModels.Bot.destroy).toHaveBeenCalledWith({ where: { id: mockBotId } });
      expect(logger.info).toHaveBeenCalledWith(`Bot deleted: ${mockBotId}`);
      expect(result).toBe(true);
    });

    it("should return false if bot not found during deletion", async () => {
      (mockModels.Bot.destroy as jest.Mock).mockResolvedValue(0);

      const result = await botService.deleteBot("non-existent-id");

      expect(mockModels.Bot.destroy).toHaveBeenCalledWith({ where: { id: "non-existent-id" } });
      expect(logger.info).not.toHaveBeenCalledWith(`Bot deleted: non-existent-id`);
      expect(result).toBe(false);
    });

    it("should handle errors during deletion", async () => {
      const error = new Error("Database error");
      (mockModels.Bot.destroy as jest.Mock).mockRejectedValue(error);

      await expect(botService.deleteBot(mockBotId)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(`Error deleting bot ${mockBotId}:`, error);
    });
  });

  describe("activateBot", () => {
    const mockUserId = "user-123";

    it("should activate a bot successfully", async () => {
      const mockBotInstance = {
        id: mockBotId,
        status: BotStatus.DRAFT,
        update: jest.fn().mockResolvedValue(true),
        toJSON: () => ({ id: mockBotId, status: BotStatus.ACTIVE }),
      };
      (mockModels.Bot.findByPk as jest.Mock).mockResolvedValue(mockBotInstance);

      const result = await botService.activateBot(mockBotId, mockUserId);

      expect(mockModels.Bot.findByPk).toHaveBeenCalledWith(mockBotId);
      expect(mockBotInstance.update).toHaveBeenCalledWith({
        status: BotStatus.ACTIVE,
        updatedBy: mockUserId,
      });
      expect(logger.info).toHaveBeenCalledWith(
        `Bot status updated: ${mockBotId} - ${BotStatus.ACTIVE}`,
      );
      expect(result).toEqual({ id: mockBotId, status: BotStatus.ACTIVE });
    });

    it("should return null if bot not found during activation", async () => {
      (mockModels.Bot.findByPk as jest.Mock).mockResolvedValue(null);

      const result = await botService.activateBot("non-existent-id", mockUserId);

      expect(mockModels.Bot.findByPk).toHaveBeenCalledWith("non-existent-id");
      expect(result).toBeNull();
    });

    it("should handle errors during activation", async () => {
      const error = new Error("Database error");
      (mockModels.Bot.findByPk as jest.Mock).mockRejectedValue(error);

      await expect(botService.activateBot(mockBotId, mockUserId)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(`Error updating bot status ${mockBotId}:`, error);
    });
  });

  describe("deactivateBot", () => {
    const mockUserId = "user-123";

    it("should deactivate a bot successfully", async () => {
      const mockBotInstance = {
        id: mockBotId,
        status: BotStatus.ACTIVE,
        update: jest.fn().mockResolvedValue(true),
        toJSON: () => ({ id: mockBotId, status: BotStatus.INACTIVE }),
      };
      (mockModels.Bot.findByPk as jest.Mock).mockResolvedValue(mockBotInstance);

      const result = await botService.deactivateBot(mockBotId, mockUserId);

      expect(mockModels.Bot.findByPk).toHaveBeenCalledWith(mockBotId);
      expect(mockBotInstance.update).toHaveBeenCalledWith({
        status: BotStatus.INACTIVE,
        updatedBy: mockUserId,
      });
      expect(logger.info).toHaveBeenCalledWith(
        `Bot status updated: ${mockBotId} - ${BotStatus.INACTIVE}`,
      );
      expect(result).toEqual({ id: mockBotId, status: BotStatus.INACTIVE });
    });

    it("should return null if bot not found during deactivation", async () => {
      (mockModels.Bot.findByPk as jest.Mock).mockResolvedValue(null);

      const result = await botService.deactivateBot("non-existent-id", mockUserId);

      expect(mockModels.Bot.findByPk).toHaveBeenCalledWith("non-existent-id");
      expect(result).toBeNull();
    });

    it("should handle errors during deactivation", async () => {
      const error = new Error("Database error");
      (mockModels.Bot.findByPk as jest.Mock).mockRejectedValue(error);

      await expect(botService.deactivateBot(mockBotId, mockUserId)).rejects.toThrow(error);
      expect(logger.error).toHaveBeenCalledWith(`Error updating bot status ${mockBotId}:`, error);
    });
  });

  describe("Channel Integration", () => {
    it("should return mock channel config", async () => {
      const channelConfig = await botService.getChannelConfig(mockBotId, "web");
      expect(channelConfig).toEqual({
        channelType: "web",
        isActive: true,
        config: {
          webhookUrl: "https://api.example.com/webhooks/web",
          apiKey: "mock-api-key",
        },
      });
    });

    it("should create channel integration", async () => {
      const channelData = { channelType: "whatsapp", config: { token: "abc" } };
      const integration = await botService.createChannelIntegration(mockBotId, channelData);
      expect(integration).toEqual(
        expect.objectContaining({
          botId: mockBotId,
          channelType: "whatsapp",
          config: { token: "abc" },
        }),
      );
      expect(integration.id).toBeDefined();
      expect(integration.createdAt).toBeDefined();
    });

    it("should update channel integration", async () => {
      const channelId = "channel-456";
      const updateData = { config: { token: "xyz" } };
      const integration = await botService.updateChannelIntegration(
        mockBotId,
        channelId,
        updateData,
      );
      expect(integration).toEqual(
        expect.objectContaining({
          id: channelId,
          botId: mockBotId,
          config: { token: "xyz" },
        }),
      );
      expect(integration.updatedAt).toBeDefined();
    });
  });

  describe("Bot Cloning, Export, and Import", () => {
    const mockExportedData = {
      exportVersion: "1.0",
      exportedAt: new Date().toISOString(),
      bot: {
        id: "original-bot",
        name: "Original Bot",
        status: BotStatus.DRAFT,
        createdAt: new Date(), // Change to Date object
        updatedAt: new Date(), // Change to Date object
      },
      languages: [],
      flows: [],
      intents: [],
      faqCategories: [],
    };
    const mockUserId = "user-123";

    beforeEach(() => {
      // Mock the private methods for the existing tests
      (botService as any)._getBotExportData = jest.fn().mockResolvedValue(mockExportedData);
      (botService as any)._importBotData = jest.fn().mockResolvedValue({ id: "new-bot" });
    });

    describe("cloneBot", () => {
      it("should clone a bot successfully", async () => {
        const result = await botService.cloneBot("original-bot", mockUserId);
        expect((botService as any)._getBotExportData).toHaveBeenCalledWith(
          "original-bot",
          expect.any(Object),
        );
        expect((botService as any)._importBotData).toHaveBeenCalledWith(
          mockExportedData,
          mockUserId,
          expect.any(Object),
          { isClone: true },
        );
        expect(result).toEqual({ id: "new-bot" });
      });

      it("should return null if original bot not found during clone", async () => {
        (botService as any)._getBotExportData.mockResolvedValue(null);
        const result = await botService.cloneBot("non-existent-bot", mockUserId);
        expect(result).toBeNull();
      });

      it("should handle errors during clone", async () => {
        const error = new Error("Clone failed");
        (botService as any)._getBotExportData.mockRejectedValue(error);
        await expect(botService.cloneBot("original-bot", mockUserId)).rejects.toThrow(error);
        expect(logger.error).toHaveBeenCalledWith("Error cloning bot original-bot:", error);
      });
    });

    describe("exportBotData", () => {
      it("should export bot data successfully", async () => {
        const result = await botService.exportBotData("original-bot");
        expect((botService as any)._getBotExportData).toHaveBeenCalledWith("original-bot");
        expect(result).toEqual(mockExportedData);
      });

      it("should return null if bot not found during export", async () => {
        (botService as any)._getBotExportData.mockResolvedValue(null);
        const result = await botService.exportBotData("non-existent-bot");
        expect(result).toBeNull();
      });
    });

    describe("importBotData", () => {
      it("should import bot data successfully", async () => {
        const result = await botService.importBotData(mockExportedData, mockUserId);
        expect((botService as any)._importBotData).toHaveBeenCalledWith(
          mockExportedData,
          mockUserId,
          expect.any(Object),
        );
        expect(result).toEqual({ id: "new-bot" });
      });

      it("should handle errors during import", async () => {
        const error = new Error("Import failed");
        (botService as any)._importBotData.mockRejectedValue(error);
        await expect(botService.importBotData(mockExportedData, mockUserId)).rejects.toThrow(error);
        expect(logger.error).toHaveBeenCalledWith("Error importing bot:", error);
      });
    });
  });

  describe("publishBot", () => {
    const mockBotId = "bot-123";
    const mockModelId = "model-456";
    const mockFlows = [
      { appId: "app-1", name: "Flow 1" },
      { appId: "app-2", name: "Flow 2" },
    ];

    beforeEach(() => {
      const mockBotInstance = {
        id: mockBotId,
        trainingJobs: [{ id: mockModelId, modelUrl: "http://model.url" }],
        update: jest.fn().mockResolvedValue(true),
        toJSON: jest.fn(() => ({
          id: mockBotId,
          trainingJobs: [{ id: mockModelId, modelUrl: "http://model.url" }],
        })),
      };

      mockModels.Bot.findByPk = jest.fn().mockResolvedValue(mockBotInstance);
      mockFlowService.getFlowsByBotId = jest.fn().mockResolvedValue(mockFlows);
      mockStudioAppsService.bulkPublishApps = jest.fn().mockResolvedValue({
        published: [{ appId: "app-1" }, { appId: "app-2" }],
        failed: [],
      });
    });

    it("should publish bot successfully", async () => {
      const mockBotInstance = {
        id: mockBotId,
        trainingJobs: [{ id: mockModelId, modelUrl: "http://model.url" }],
        update: jest.fn(), // Mock the update method
        toJSON: jest.fn(() => ({
          id: mockBotId,
          trainingJobs: [{ id: mockModelId, modelUrl: "http://model.url" }],
        })),
      };
      mockModels.Bot.findByPk = jest.fn().mockResolvedValue(mockBotInstance); // Ensure findByPk returns this instance

      const result = await botService.publishBot(mockBotId);

      expect(mockModels.Bot.findByPk).toHaveBeenCalledWith(mockBotId, expect.any(Object));
      expect(mockFlowService.getFlowsByBotId).toHaveBeenCalledWith(mockBotId);
      expect(mockStudioAppsService.bulkPublishApps).toHaveBeenCalledWith(["app-1", "app-2"]);
      expect(mockBotInstance.update).toHaveBeenCalledWith({
        publishedModelId: mockModelId,
        status: BotStatus.ACTIVE,
      });
      expect(logger.info).toHaveBeenCalledWith(`Published 2 apps for bot ${mockBotId}`);
      expect(logger.info).toHaveBeenCalledWith(
        `Bot ${mockBotId} published with model ID: ${mockModelId}`,
      );
      expect(result).toEqual({
        message: `Bot ${mockBotId} published successfully with model ID ${mockModelId}.`,
      });
    });

    it("should throw error if bot not found", async () => {
      mockModels.Bot.findByPk = jest.fn().mockResolvedValue(null);
      await expect(botService.publishBot(mockBotId)).rejects.toThrow("Bot not found.");
      expect(logger.error).toHaveBeenCalledWith(
        `Error publishing bot ${mockBotId}:`,
        expect.any(Error),
      );
    });

    it("should throw error if no built model found", async () => {
      mockModels.Bot.findByPk = jest.fn().mockResolvedValue({
        id: mockBotId,
        trainingJobs: [],
        update: jest.fn(),
      });
      await expect(botService.publishBot(mockBotId)).rejects.toThrow(
        "No build model found for this bot.",
      );
      expect(logger.error).toHaveBeenCalledWith(
        `Error publishing bot ${mockBotId}:`,
        expect.any(Error),
      );
    });

    it("should throw error if bulkPublishApps fails", async () => {
      mockStudioAppsService.bulkPublishApps = jest.fn().mockResolvedValue({
        published: [],
        failed: [{ appId: "app-1", reason: "Publish failed" }],
      });
      await expect(botService.publishBot(mockBotId)).rejects.toThrow(
        "Failed to publish Bot: \nFlow Flow 1: Publish failed",
      );
      expect(logger.error).toHaveBeenCalledWith(
        `Error publishing bot ${mockBotId}:`,
        expect.any(Error),
      );
    });
  });

  describe("Private Methods - _getBotExportData", () => {
    beforeEach(() => {
      // Clear existing mocks for private method tests
      jest.clearAllMocks();
    });

    it("should export bot data with all related entities", async () => {
      const mockBot = {
        id: mockBotId,
        name: "Test Bot",
        toJSON: () => ({ id: mockBotId, name: "Test Bot" }),
        botLanguages: [{ id: "lang1", toJSON: () => ({ id: "lang1", languageCode: "en" }) }],
        flows: [{ id: "flow1", appId: "app1", toJSON: () => ({ id: "flow1", name: "Flow 1" }) }],
        faqCategories: [
          {
            id: "cat1",
            toJSON: () => ({ id: "cat1", name: "Category 1" }),
            faqItems: [
              {
                id: "item1",
                toJSON: () => ({ id: "item1", question: "Question 1" }),
                faqTranslations: [
                  { id: "trans1", toJSON: () => ({ id: "trans1", language: "en" }) },
                ],
              },
            ],
          },
        ],
        intentItems: [
          {
            id: "intent1",
            toJSON: () => ({ id: "intent1", name: "Intent 1" }),
            entities: [{ id: "entity1", toJSON: () => ({ id: "entity1", name: "Entity 1" }) }],
            intentUtterances: [
              {
                id: "utt1",
                toJSON: () => ({ id: "utt1", text: "Utterance 1" }),
                utteranceTranslations: [{ id: "uttTrans1", toJSON: () => ({ id: "uttTrans1" }) }],
              },
            ],
          },
        ],
      };

      mockModels.Bot.findByPk = jest.fn().mockResolvedValue(mockBot);
      mockStudioAppsService.exportApp = jest.fn().mockResolvedValue({ id: "app1", name: "App 1" });

      const result = await (botService as any)._getBotExportData(mockBotId);

      expect(result).toEqual({
        exportVersion: "1.0",
        exportedAt: expect.any(String),
        bot: { id: mockBotId, name: "Test Bot" },
        languages: [{ id: "lang1", languageCode: "en" }],
        flows: [
          {
            flowData: { id: "flow1", name: "Flow 1" },
            appData: { id: "app1", name: "App 1" },
          },
        ],
        faqCategories: [
          {
            category: { id: "cat1", name: "Category 1" },
            items: [
              {
                item: { id: "item1", question: "Question 1" },
                translations: [{ id: "trans1", language: "en" }],
              },
            ],
          },
        ],
        intents: [
          {
            intent: { id: "intent1", name: "Intent 1" },
            entities: [{ id: "entity1", name: "Entity 1" }],
            utterances: [
              {
                utterance: { id: "utt1", text: "Utterance 1" },
                translations: [{ id: "uttTrans1" }],
              },
            ],
          },
        ],
      });
    });

    it("should return null if bot not found", async () => {
      mockModels.Bot.findByPk = jest.fn().mockResolvedValue(null);

      const result = await (botService as any)._getBotExportData("non-existent-bot");

      expect(result).toBeNull();
    });

    it("should handle empty related entities", async () => {
      const mockBot = {
        id: mockBotId,
        name: "Test Bot",
        toJSON: () => ({ id: mockBotId, name: "Test Bot" }),
        botLanguages: [],
        flows: [],
        faqCategories: [],
        intentItems: [],
      };

      mockModels.Bot.findByPk = jest.fn().mockResolvedValue(mockBot);

      const result = await (botService as any)._getBotExportData(mockBotId);

      expect(result).toEqual({
        exportVersion: "1.0",
        exportedAt: expect.any(String),
        bot: { id: mockBotId, name: "Test Bot" },
        languages: [],
        flows: [],
        faqCategories: [],
        intents: [],
      });
    });
  });

  describe("Private Methods - _importBotData", () => {
    beforeEach(() => {
      // Clear existing mocks for private method tests
      jest.clearAllMocks();
      (uuidv4 as jest.Mock).mockReturnValue("new-uuid");
      mockStudioAppsService.createApp = jest.fn().mockResolvedValue({ id: "new-app-id" });
    });

    it("should import basic bot data successfully", async () => {
      const mockTransaction = {};
      const mockUserId = "user-123";
      const mockImportData = {
        exportVersion: "1.0",
        exportedAt: new Date().toISOString(),
        bot: { id: "original-bot", name: "Original Bot", description: "Test bot" },
        languages: [],
        flows: [],
        faqCategories: [],
        intents: [],
      };

      const mockNewBot = {
        id: "new-bot-id",
        toJSON: () => ({ id: "new-bot-id", name: "Imported - Original Bot new-" }),
      };

      mockModels.Bot.create = jest.fn().mockResolvedValue(mockNewBot);
      mockModels.BotLanguage.bulkCreate = jest.fn().mockResolvedValue([]);

      const result = await (botService as any)._importBotData(
        mockImportData,
        mockUserId,
        mockTransaction,
      );

      expect(mockModels.Bot.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: "new-uuid",
          name: expect.stringContaining("Imported - Original Bot"),
          description: "Test bot",
          createdBy: mockUserId,
          updatedBy: mockUserId,
        }),
        { transaction: mockTransaction },
      );

      expect(result).toEqual({ id: "new-bot-id", name: "Imported - Original Bot new-" });
    });

    it("should handle clone option correctly", async () => {
      const mockTransaction = {};
      const mockUserId = "user-123";
      const mockImportData = {
        exportVersion: "1.0",
        exportedAt: new Date().toISOString(),
        bot: { id: "original-bot", name: "Original Bot", description: "Test bot" },
        languages: [],
        flows: [],
        faqCategories: [],
        intents: [],
      };

      const mockNewBot = {
        id: "new-bot-id",
        toJSON: () => ({ id: "new-bot-id", name: "Original Bot - Clone" }),
      };

      mockModels.Bot.create = jest.fn().mockResolvedValue(mockNewBot);
      mockModels.BotLanguage.bulkCreate = jest.fn().mockResolvedValue([]);

      const result = await (botService as any)._importBotData(
        mockImportData,
        mockUserId,
        mockTransaction,
        { isClone: true },
      );

      expect(mockModels.Bot.create).toHaveBeenCalledWith(
        expect.objectContaining({
          name: "Original Bot - Clone",
        }),
        { transaction: mockTransaction },
      );

      expect(result).toEqual({ id: "new-bot-id", name: "Original Bot - Clone" });
    });

    it("should handle import with flows that have flowId references", async () => {
      const mockTransaction = { commit: jest.fn(), rollback: jest.fn() };
      const mockUserId = "user-123";
      const mockImportDataWithFlows = {
        exportVersion: "1.0",
        exportedAt: new Date(),
        languages: ["en"],
        bot: { id: "old-bot-id", name: "Test Bot" },
        flows: [
          {
            flowData: { id: "flow-1", name: "Test Flow", botId: "old-bot-id" },
            appData: { id: "app-1", name: "Test App", ngage_id: "ngage-1" },
          },
        ],
        intents: [
          {
            intent: { id: "intent-1", name: "Test Intent", flowId: "flow-1" },
            entities: [],
            utterances: [],
          },
        ],
        faqCategories: [],
      };

      mockModels.Bot.create = jest.fn().mockResolvedValue({
        id: "new-bot-id",
        name: "Test Bot",
        toJSON: () => ({ id: "new-bot-id", name: "Test Bot" }),
      });
      mockModels.BotLanguage.bulkCreate = jest.fn().mockResolvedValue([]);
      mockModels.Flow.create = jest.fn().mockResolvedValue({ id: "new-flow-id" });
      mockModels.IntentItems.create = jest.fn().mockResolvedValue({ id: "new-intent-id" });
      mockStudioAppsService.createApp = jest.fn().mockResolvedValue({ id: "new-app-id" });

      const result = await (botService as any)._importBotData(
        mockImportDataWithFlows,
        mockUserId,
        mockTransaction,
      );

      expect(mockModels.Flow.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.any(String),
          botId: "new-bot-id",
          appId: "new-app-id",
        }),
        { transaction: mockTransaction },
      );

      expect(mockModels.IntentItems.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.any(String),
          botId: "new-bot-id",
          flowId: "new-flow-id", // Should be mapped to new flow ID
        }),
        { transaction: mockTransaction },
      );

      expect(result).toEqual({ id: "new-bot-id", name: "Test Bot" });
    });

    it("should handle import with intents that have no flowId", async () => {
      const mockTransaction = { commit: jest.fn(), rollback: jest.fn() };
      const mockUserId = "user-123";
      const mockImportDataWithIntents = {
        exportVersion: "1.0",
        exportedAt: new Date(),
        languages: ["en"],
        bot: { id: "old-bot-id", name: "Test Bot" },
        flows: [],
        intents: [
          {
            intent: { id: "intent-1", name: "Test Intent", flowId: null },
            entities: [{ id: "entity-1", name: "test_entity" }],
            utterances: [
              {
                utterance: { id: "utterance-1", text: "Hello" },
                translations: [{ id: "trans-1", language: "en", text: "Hello" }],
              },
            ],
          },
        ],
        faqCategories: [],
      };

      mockModels.Bot.create = jest.fn().mockResolvedValue({
        id: "new-bot-id",
        name: "Test Bot",
        toJSON: () => ({ id: "new-bot-id", name: "Test Bot" }),
      });
      mockModels.BotLanguage.bulkCreate = jest.fn().mockResolvedValue([]);
      mockModels.IntentItems.create = jest.fn().mockResolvedValue({ id: "new-intent-id" });
      mockModels.Entities.bulkCreate = jest.fn().mockResolvedValue([]);
      mockModels.IntentUtterance.create = jest.fn().mockResolvedValue({ id: "new-utterance-id" });
      mockModels.UtteranceTranslation.bulkCreate = jest.fn().mockResolvedValue([]);

      const result = await (botService as any)._importBotData(
        mockImportDataWithIntents,
        mockUserId,
        mockTransaction,
      );

      expect(mockModels.IntentItems.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.any(String),
          botId: "new-bot-id",
          flowId: undefined, // Should be undefined when no flowId mapping exists
        }),
        { transaction: mockTransaction },
      );

      expect(mockModels.Entities.bulkCreate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            botId: "new-bot-id",
            intentId: "new-intent-id",
          }),
        ]),
        { transaction: mockTransaction },
      );

      expect(result).toEqual({ id: "new-bot-id", name: "Test Bot" });
    });

    it("should handle import with FAQ categories and translations", async () => {
      const mockTransaction = { commit: jest.fn(), rollback: jest.fn() };
      const mockUserId = "user-123";
      const mockImportDataWithFAQ = {
        exportVersion: "1.0",
        exportedAt: new Date(),
        languages: ["en"],
        bot: { id: "old-bot-id", name: "Test Bot" },
        flows: [],
        intents: [],
        faqCategories: [
          {
            category: { id: "cat-1", name: "General", botId: "old-bot-id" },
            items: [
              {
                item: { id: "item-1", question: "What is this?", answer: "This is a test" },
                translations: [
                  {
                    id: "trans-1",
                    language: "en",
                    question: "What is this?",
                    answer: "This is a test",
                  },
                ],
              },
            ],
          },
        ],
      };

      mockModels.Bot.create = jest.fn().mockResolvedValue({
        id: "new-bot-id",
        name: "Test Bot",
        toJSON: () => ({ id: "new-bot-id", name: "Test Bot" }),
      });
      mockModels.BotLanguage.bulkCreate = jest.fn().mockResolvedValue([]);
      mockModels.FaqCategory.create = jest.fn().mockResolvedValue({ id: "new-cat-id" });
      mockModels.FaqItems.create = jest.fn().mockResolvedValue({ id: "new-item-id" });
      mockModels.FaqTranslation.bulkCreate = jest.fn().mockResolvedValue([]);

      const result = await (botService as any)._importBotData(
        mockImportDataWithFAQ,
        mockUserId,
        mockTransaction,
      );

      expect(mockModels.FaqCategory.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.any(String),
          name: "General",
          botId: "new-bot-id",
        }),
        { transaction: mockTransaction },
      );

      expect(mockModels.FaqItems.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: expect.any(String),
          categoryId: "new-cat-id",
        }),
        { transaction: mockTransaction },
      );

      expect(mockModels.FaqTranslation.bulkCreate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            faqId: "new-item-id",
            language: "en",
            question: "What is this?",
            answer: "This is a test",
          }),
        ]),
        { transaction: mockTransaction },
      );

      expect(result).toEqual({ id: "new-bot-id", name: "Test Bot" });
    });
  });
});
