import { Router } from "express";
import { EntitiesController } from "../controllers/entities.controller";
import { authenticateMiddleware } from "../middleware/authenticate.middleware";
import {
  PaginationQuerySchema,
  UuidParamSchema,
  validateBody,
  validateParams,
  validateQuery,
} from "@neuratalk/common";
import { CreateEntitySchema, UpdateEntitySchema } from "../schemas/entity.schemas";

import { AppContext } from "../types/context.types";
import { BotIdParamSchema } from "../schemas";
import { authorizeMiddleware } from "../middleware/authorize.middleware";
import { PermissionKeys } from "../types";

export function createEntityRoutes(context: AppContext): Router {
  const router = Router();

  const entitiesController = new EntitiesController(context);

  router.post(
    "/entities",
    authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]),
    validateBody(CreateEntitySchema),
    entitiesController.create,
  );
  router.get(
    "/bots/:botId/entities",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateParams(BotIdParamSchema),
    validateQuery(PaginationQuerySchema),
    entitiesController.getByBotId,
  );
  router.get("/entities/:id", authorizeMiddleware([PermissionKeys.READ_NEURATALK]), validateParams(UuidParamSchema), entitiesController.getById);
  router.put(
    "/entities/:id",
    authorizeMiddleware([PermissionKeys.UPDATE_NEURATALK]),
    validateParams(UuidParamSchema),
    validateBody(UpdateEntitySchema),
    entitiesController.update,
  );
  router.delete("/entities/:id", authorizeMiddleware([PermissionKeys.DELETE_NEURATALK]), validateParams(UuidParamSchema), entitiesController.delete);

  return router;
}
