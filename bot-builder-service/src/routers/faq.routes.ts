import { Router } from "express";
import { FaqCategoryController } from "../controllers/faq-category.controller";
import { FaqItemsController } from "../controllers/faq-items.controller";
import { FaqTranslationController } from "../controllers/faq-translation.controller";
import {
  PaginationQuerySchema,
  UuidParamSchema,
  validateBody,
  validateParams,
  validateQuery,
} from "@neuratalk/common";
import {
  CreateFaqCategorySchema,
  UpdateFaqCategorySchema,
  CreateFaqItemSchema,
  UpdateFaqItemSchema,
  CreateFaqTranslationSchema,
  UpdateFaqTranslationSchema,
  FaqIdParamSchema,
  FaqTranslationByLangParamSchema,
  FaqsByCategoryAndLanguageParamSchema,
} from "../schemas/faq.schemas";

import { AppContext } from "../types/context.types";
import { validateFaqQuestions } from "../middleware/faqValidation.middleware";
import { authenticateMiddleware } from "../middleware/authenticate.middleware";
import { authorizeMiddleware } from "../middleware/authorize.middleware";
import { PermissionKeys } from "../types";

export function createFaqRoutes(context: AppContext): Router {
  const router = Router();

  const faqCategoryController = new FaqCategoryController(context);
  const faqItemsController = new FaqItemsController(context);
  const faqTranslationController = new FaqTranslationController(context);
  // FAQ Category routes
  router.post(
    "/faq-categories",
    authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]),
    validateBody(CreateFaqCategorySchema),
    faqCategoryController.create,
  );
  router.get("/faq-categories", authorizeMiddleware([PermissionKeys.READ_NEURATALK]), validateQuery(PaginationQuerySchema), faqCategoryController.getAll);
  router.get("/faq-categories/:id", authorizeMiddleware([PermissionKeys.READ_NEURATALK]), validateParams(UuidParamSchema), faqCategoryController.getById);
  router.put(
    "/faq-categories/:id",
    authorizeMiddleware([PermissionKeys.UPDATE_NEURATALK]),
    validateParams(UuidParamSchema),
    validateBody(UpdateFaqCategorySchema),
    faqCategoryController.update,
  );
  router.delete(
    "/faq-categories/:id",
    authorizeMiddleware([PermissionKeys.DELETE_NEURATALK]),
    validateParams(UuidParamSchema),
    faqCategoryController.delete,
  );

  // FAQ Items routes
  router.post("/faq-items", authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]), validateBody(CreateFaqItemSchema), faqItemsController.create);
  router.get("/faq-items", authorizeMiddleware([PermissionKeys.READ_NEURATALK]), validateQuery(PaginationQuerySchema), faqItemsController.getAll);
  router.get("/faq-items/:id", authorizeMiddleware([PermissionKeys.READ_NEURATALK]), validateParams(UuidParamSchema), faqItemsController.getById);
  router.put(
    "/faq-items/:id",
    authorizeMiddleware([PermissionKeys.UPDATE_NEURATALK]),
    validateParams(UuidParamSchema),
    validateBody(UpdateFaqItemSchema),
    faqItemsController.update,
  );
  router.delete("/faq-items/:id", authorizeMiddleware([PermissionKeys.DELETE_NEURATALK]), validateParams(UuidParamSchema), faqItemsController.delete);

  // FAQ Translation routes
  router.post(
    "/faq-translations",
    authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]),
    validateBody(CreateFaqTranslationSchema),
    validateFaqQuestions(context),
    faqTranslationController.createFaqTranslation,
  );
  router.put(
    "/faq-translations/:id",
    authorizeMiddleware([PermissionKeys.UPDATE_NEURATALK]),
    validateParams(UuidParamSchema),
    validateBody(UpdateFaqTranslationSchema),
    validateFaqQuestions(context),
    faqTranslationController.updateFaqTranslation,
  );
  router.get(
    "/faqs/category/:categoryId/language/:langId",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateParams(FaqsByCategoryAndLanguageParamSchema),
    validateQuery(PaginationQuerySchema),
    faqTranslationController.getFaqsByCategoryAndLanguage,
  );
  router.get(
    "/faq-translations/:id",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateParams(UuidParamSchema),
    faqTranslationController.getById,
  );
  router.get(
    "/faq/:faqId/translations",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateParams(FaqIdParamSchema),
    validateQuery(PaginationQuerySchema),
    faqTranslationController.getTranslationsByFaqId,
  );
  router.get(
    "/faq/:faqId/lang/:langId/translation",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateParams(FaqTranslationByLangParamSchema),
    faqTranslationController.getTranslationByFaqIdAndLangId,
  );

  router.delete(
    "/faq-translations/:id",
    authorizeMiddleware([PermissionKeys.DELETE_NEURATALK]),
    validateParams(UuidParamSchema),
    faqTranslationController.delete,
  );

  return router;
}
