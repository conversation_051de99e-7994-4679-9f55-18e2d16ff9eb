import { Router } from "express";
import { IntentItemsController } from "../controllers/intent-items.controller";
import { UtteranceTranslationController } from "../controllers/utterance-translation.controller";
import {
  IncludeQuerySchema,
  PaginationQuerySchema,
  UuidParamSchema,
  validateBody,
  validateParams,
  validateQuery,
} from "@neuratalk/common";
import {
  CreateIntentItemSchema,
  UpdateIntentItemSchema,
  CreateUtteranceTranslationSchema,
  UpdateUtteranceTranslationSchema,
  AssignFlowToIntentSchema,
  UtteranceTranslationParamSchema,
  UtteranceTranslationByLangParamSchema,
} from "../schemas/intent.schemas";

import { AppContext } from "../types/context.types";
import { authenticateMiddleware } from "../middleware/authenticate.middleware";
import { authorizeMiddleware } from "../middleware/authorize.middleware";
import { PermissionKeys } from "../types";

export function createIntentRoutes(context: AppContext): Router {
  const router = Router();

  const intentItemsController = new IntentItemsController(context);
  const intentUtteranceTranslationController = new UtteranceTranslationController(context);
  // Intent Items routes
  router.post(
    "/intent-items",
    authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]),
    validateBody(CreateIntentItemSchema),
    intentItemsController.create,
  );
  router.get("/intent-items", authorizeMiddleware([PermissionKeys.READ_NEURATALK]), validateQuery(PaginationQuerySchema), intentItemsController.getAll);
  router.get("/intent-items/:id", authorizeMiddleware([PermissionKeys.READ_NEURATALK]), validateParams(UuidParamSchema), intentItemsController.getById);
  router.put(
    "/intent-items/:id",
    authorizeMiddleware([PermissionKeys.UPDATE_NEURATALK]),
    validateParams(UuidParamSchema),
    validateBody(UpdateIntentItemSchema),
    intentItemsController.update,
  );
  router.delete("/intent-items/:id", authorizeMiddleware([PermissionKeys.DELETE_NEURATALK]), validateParams(UuidParamSchema), intentItemsController.delete);
  router.post(
    "/intent-items/assign-flow",
    authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]),
    validateBody(AssignFlowToIntentSchema),
    intentItemsController.assignFlowToIntent,
  );

  // Intent Utterance Translation routes
  router.post(
    "/intent/:intentId/lang/:langId/intent-utterance",
    authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]),
    validateBody(CreateUtteranceTranslationSchema),
    validateParams(UtteranceTranslationParamSchema),
    intentUtteranceTranslationController.create,
  );
  router.get(
    "/intent/:intentId/lang/:langId/intent-utterance",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateQuery(PaginationQuerySchema),
    validateParams(UtteranceTranslationParamSchema),
    intentUtteranceTranslationController.getAll,
  );

  router.get(
    "/intent-utterance/:id",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateParams(UuidParamSchema),
    validateQuery(IncludeQuerySchema),
    intentUtteranceTranslationController.getById,
  );
  router.get(
    "/utterance/:utteranceId/lang/:langId/translation",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateParams(UtteranceTranslationByLangParamSchema),
    intentUtteranceTranslationController.getTranslationByUtteranceIdAndLangId,
  );
  router.put(
    "/intent-utterance/:id",
    authorizeMiddleware([PermissionKeys.UPDATE_NEURATALK]),
    validateParams(UuidParamSchema),
    validateBody(UpdateUtteranceTranslationSchema),
    intentUtteranceTranslationController.update,
  );
  router.delete(
    "/intent-utterance/:id",
    authorizeMiddleware([PermissionKeys.DELETE_NEURATALK]),
    validateParams(UuidParamSchema),
    intentUtteranceTranslationController.delete,
  );

  return router;
}
