import { Router } from "express";
import { AppContext } from "../types/context.types";
import { FlowController } from "../controllers/flow.controller";
import { validateBody, validateParams, validateQuery } from "@neuratalk/common";
import {
  FlowIdParamSchema,
  UpdateFlowRequestSchema,
  FlowAppIdParamSchema,
  GetFlowsQuerySchema,
  BotIdParamSchema,
  FlowBotIdParamSchema,
  BulkCreateFlowsRequestSchema,
  CreateFlowPayloadSchema,
} from "../schemas";
import { authenticateMiddleware } from "../middleware/authenticate.middleware";
import { authorizeMiddleware } from "../middleware/authorize.middleware";
import { PermissionKeys } from "../types";

export function createFlowRoutes(context: AppContext): Router {
  const router = Router();
  const flowController = new FlowController(context);

  router.post("/flows", authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]), validateBody(CreateFlowPayloadSchema), flowController.createFlow);
  router.get("/flows/:id", authorizeMiddleware([PermissionKeys.READ_NEURATALK]), validateParams(FlowIdParamSchema), flowController.getFlowById);
  router.put(
    "/flows/:id",
    authorizeMiddleware([PermissionKeys.UPDATE_NEURATALK]),
    validateParams(FlowIdParamSchema),
    validateBody(UpdateFlowRequestSchema),
    flowController.updateFlow,
  );
  router.delete(
    "/flows/:id/apps/:appId",
    authorizeMiddleware([PermissionKeys.DELETE_NEURATALK]),
    validateParams(FlowAppIdParamSchema),
    flowController.deleteFlow,
  );
  router.get(
    "/bots/:botId/flows",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateParams(FlowBotIdParamSchema),
    validateQuery(GetFlowsQuerySchema),
    flowController.getFlowsByBot,
  );

  router.post(
    "/flows/:id/clone",
    authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]),
    validateParams(FlowIdParamSchema),
    flowController.cloneFlow,
  );

  return router;
}