import { Router } from "express";
import { AppContext } from "../types/context.types";
import { BotController } from "../controllers/bot.controller";
import {
  validateBody,
  validateParams,
  validateQuery,
  PaginationQuerySchema,
} from "@neuratalk/common";
import {
  CreateBotRequestSchema,
  UpdateBotRequestSchema,
  BotIdParamSchema,
  BotChannelParamSchema,
  CreateChannelIntegrationSchema,
  BotChannelIdParamSchema,
  UpdateChannelIntegrationSchema,
} from "../schemas";
import { authenticateMiddleware } from "../middleware/authenticate.middleware";
import { authorizeMiddleware } from "../middleware/authorize.middleware";
import { PermissionKeys } from "../types";

export function createBotRoutes(context: AppContext): Router {
  const router = Router();
  const botController = new BotController(context);

  router.post("/bots", authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]), validateBody(CreateBotRequestSchema), botController.createBot);
  router.get("/bots/:botId", authorizeMiddleware([PermissionKeys.READ_NEURATALK]), validateParams(BotIdParamSchema), botController.getBotById);
  router.put(
    "/bots/:botId",
    authorizeMiddleware([PermissionKeys.UPDATE_NEURATALK]),
    validateParams(BotIdParamSchema),
    validateBody(UpdateBotRequestSchema),
    botController.updateBot,
  );
  router.delete("/bots/:botId", authorizeMiddleware([PermissionKeys.DELETE_NEURATALK]), validateParams(BotIdParamSchema), botController.deleteBot);
  router.get("/bots", authorizeMiddleware([PermissionKeys.READ_NEURATALK]), validateQuery(PaginationQuerySchema), botController.getBots);
  router.post("/bots/:botId/activate", authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]), validateParams(BotIdParamSchema), botController.activateBot);
  router.post(
    "/bots/:botId/deactivate",
    authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]),
    validateParams(BotIdParamSchema),
    botController.deactivateBot,
  );


  router.post("/bots/:botId/clone", authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]), validateParams(BotIdParamSchema), botController.cloneBot);
  router.post("/bots/:botId/export", authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]), validateParams(BotIdParamSchema), botController.exportBot);
  router.post("/bots/import", authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]), botController.importBot);


  // Channel routes
  router.get(
    "/bots/:botId/channels/:channelType",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateParams(BotChannelParamSchema),
    botController.getChannelConfig,
  );
  router.post(
    "/bots/:botId/channels",
    authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]),
    validateParams(BotIdParamSchema),
    validateBody(CreateChannelIntegrationSchema),
    botController.createChannelIntegration,
  );
  router.put(
    "/bots/:botId/channels/:channelId",
    authorizeMiddleware([PermissionKeys.UPDATE_NEURATALK]),
    validateParams(BotChannelIdParamSchema),
    validateBody(UpdateChannelIntegrationSchema),
    botController.updateChannelIntegration,
  );

  router.post("/bots/:botId/publish", authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]), validateParams(BotIdParamSchema), botController.publishBot);

  return router;
}