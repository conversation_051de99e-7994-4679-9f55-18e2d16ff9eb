import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  successResponse,
  errorResponse,
} from "@neuratalk/common";
import {
  CreateBotLanguageRequest,
  BulkCreateBotLanguageRequest,
  BulkDeleteBotLanguageRequest,
  BotIdParam,
} from "../schemas";
import { logger } from "@neuratalk/common";
import { AppContext } from "../types/context.types";

export class BotLanguageController {
  private models: Models;

  constructor(private readonly context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/bot-languages/{botId}:
   *   post:
   *     summary: Assign a language to a bot
   *     tags: [Bot Languages]
   *     parameters:
   *       - in: path
   *         name: botId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateBotLanguageRequest'
   *     responses:
   *       201:
   *         description: Language assigned successfully
   */
  public create = async (
    req: Request<BotIdParam, any, CreateBotLanguageRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const { botId } = req.params;
      const { langId } = req.body;

      const botLanguage = await this.models.BotLanguage.create({
        botId,
        langId,
        isDefault: false,
        createdBy: userId,
      });

      logger.info(`Bot language assigned: ${botLanguage.id}`);
      res.status(201).json(successResponse(botLanguage));
    } catch (error) {
      logger.error("Error assigning bot language:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bot-languages/bulk:
   *   post:
   *     summary: Assign multiple languages to a bot
   *     tags: [Bot Languages]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: array
   *             items:
   *               $ref: '#/components/schemas/BulkCreateBotLanguageRequest'
   *     responses:
   *       201:
   *         description: Languages assigned successfully
   */
  public bulkCreate = async (
    req: Request<BotIdParam, any, BulkCreateBotLanguageRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const { botId } = req.params;
      const botLanguagesToCreate = req.body;

      const createdBotLanguages = await this.context.db.sequelize.transaction(async (t) => {
        const formattedLanguages = botLanguagesToCreate.ids.map((langId) => ({
          botId,
          langId,
          isDefault: false,
          createdBy: userId,
        }));
        const results = await this.models.BotLanguage.bulkCreate(formattedLanguages, {
          transaction: t,
        });
        return results;
      });

      logger.info(`Bulk bot languages assigned: ${createdBotLanguages.length} items`);
      res.status(201).json(successResponse(createdBotLanguages));
    } catch (error) {
      logger.error("Error bulk assigning bot languages:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bot-languages/bulk:
   *   delete:
   *     summary: Unassign multiple languages from a bot
   *     tags: [Bot Languages]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               ids:
   *                 type: array
   *                 items:
   *                   type: string
   *                   format: uuid
   *     responses:
   *       204:
   *         description: Languages unassigned successfully
   */
  public bulkDelete = async (
    req: Request<BotIdParam, any, BulkDeleteBotLanguageRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const { ids } = req.body;
      const { botId } = req.params;

      const deletedCount = await this.context.db.sequelize.transaction(async (t) => {
        const result = await this.models.BotLanguage.destroy({
          where: { id: ids, botId, isDefault: false },
          transaction: t,
        });
        return result;
      });

      if (deletedCount === 0) {
        res
          .status(404)
          .json(
            errorResponse({
              code: "NOT_FOUND",
              message:
                "No bot languages found for deletion or attempting to delete default language",
            }),
          );
        return;
      }

      logger.info(`Bulk bot languages unassigned: ${deletedCount} items`);
      res.status(204).send();
    } catch (error) {
      logger.error("Error bulk unassigning bot languages:", error);
      res
        .status(500)
        .json(
          errorResponse({
            code: "INTERNAL_ERROR",
            message: "Failed to bulk unassign bot languages",
          }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/bot-languages:
   *   get:
   *     summary: Get all bot languages
   *     tags: [Bot Languages]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema: { type: integer }
   *       - in: query
   *         name: limit
   *         schema: { type: integer }
   *       - in: query
   *         name: botId
   *         schema: { type: string, format: uuid }
   *       - in: query
   *         name: langId
   *         schema: { type: string, format: uuid }
   *     responses:
   *       200:
   *         description: List of bot languages
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const result = await getPaginatedResults(this.models.BotLanguage, req.query);

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching bot languages:", error);
      res
        .status(500)
        .json(errorResponse({ code: "INTERNAL_ERROR", message: "Failed to fetch bot languages" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bot-languages/{id}:
   *   get:
   *     summary: Get bot language by ID
   *     tags: [Bot Languages]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: Bot language object
   */
  public getById = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const botLanguage = await this.models.BotLanguage.findOne({
        where: { id },
      });

      if (!botLanguage) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Bot language not found" }));
        return;
      }

      res.json(successResponse(botLanguage));
    } catch (error) {
      logger.error(`Error fetching bot language ${req.params.id}:`, error);
      res
        .status(500)
        .json(errorResponse({ code: "INTERNAL_ERROR", message: "Failed to fetch bot language" }));
    }
  };

  /**
   * @swagger
   * /api/v1/bot-languages/{id}:
   *   delete:
   *     summary: Unassign a language from a bot
   *     tags: [Bot Languages]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Language unassigned successfully
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.BotLanguage.destroy({ where: { id, isDefault: false } });

      if (!deleted) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Bot language not found" }));
        return;
      }

      logger.info(`Bot language unassigned: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error unassigning bot language ${req.params.id}:`, error);
      res
        .status(500)
        .json(
          errorResponse({ code: "INTERNAL_ERROR", message: "Failed to unassign bot language" }),
        );
    }
  };
}
