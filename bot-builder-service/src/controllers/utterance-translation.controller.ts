import { Request, Response } from "express";
import {
  Language,
  Models,
  parseIncludeQuery,
  UtteranceTranslationModel,
} from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  successResponse,
  errorResponse,
  IncludeQuery,
} from "@neuratalk/common";
import {
  CreateUtteranceTranslationRequest,
  UtteranceTranslationParam,
  UpdateUtteranceTranslationRequest,
  UtteranceTranslationByLangParam,
  UtteranceIdParam,
} from "../schemas";
import { logger } from "@neuratalk/common";
import { AppContext } from "../types/context.types";
import { Op } from "sequelize";

export class UtteranceTranslationController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/utterance-translations:
   *   post:
   *     summary: Create a new utterance translation
   *     tags: [Intent Utterance Translations]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UtteranceTranslation'
   *     responses:
   *       201:
   *         description: Utterance translation created successfully
   */
  public create = async (
    req: Request<UtteranceTranslationParam, any, CreateUtteranceTranslationRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const { intentId, langId } = req.params;
      let { utteranceId, ...translationData } = req.body;

      if (!utteranceId) {
        const newUtterance = await this.models.IntentUtterance.create({
          createdBy: userId,
          intentId,
        });
        utteranceId = newUtterance.id;
        logger.info(`New IntentUtterance group created: ${utteranceId}`);
      } else {
        const existingTranslation = await this.models.UtteranceTranslation.findOne({
          where: { utteranceId, langId },
        });

        if (existingTranslation) {
          throw new Error(
            `Utterance translation for langId ${langId} already exists for utterance ${utteranceId}.`,
          );
        }
      }

      const intentUtteranceTranslation = await this.models.UtteranceTranslation.create({
        ...translationData,
        utteranceId,
        langId,
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`Utterance translation created: ${intentUtteranceTranslation.id}`);
      res.status(201).json(successResponse(intentUtteranceTranslation));
    } catch (error) {
      logger.error("Error creating utterance translation:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/utterance-translations:
   *   get:
   *     summary: Get all utterance translations
   *     tags: [Intent Utterance Translations]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema: { type: integer }
   *       - in: query
   *         name: limit
   *         schema: { type: integer }
   *       - in: query
   *         name: utteranceId
   *         schema: { type: string, format: uuid }
   *       - in: query
   *         name: langId
   *         schema: { type: string, format: uuid }
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., utterance,language)
   *     responses:
   *       200:
   *         description: List of utterance translations
   */
  public getAll = async (
    req: Request<UtteranceTranslationParam, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { intentId, langId } = req.params;
      const include = "utteranceTranslations";

      const includeAssociations = parseIncludeQuery(include, this.models);

      req.query = {
        ...req.query,
        filter: {
          ...req.query.filter,
          intentId: { eq: intentId },
        },
      };

      const paginatedResult = await getPaginatedResults(
        this.models.IntentUtterance,
        req.query,
        [],
        {
          ...includeAssociations[0],
          where: {
            langId: {
              [Op.eq]: langId,
            },
          },
        },
      );

      const result = {
        ...paginatedResult,
        items: paginatedResult.items.map((item) => item.utteranceTranslations![0].toJSON()), //TODO: check this nullish collasing operator
      };

      const utteranceIds = paginatedResult.items.map((item) => item.id);

      const translations = await this.models.UtteranceTranslation.findAll({
        where: {
          utteranceId: { [Op.in]: utteranceIds },
        },
        include: [
          {
            model: this.models.Language,
            as: "language",
            attributes: ["id", "name", "code"],
          },
        ],
      });

      const translationsByUtteranceId = translations.reduce(
        (acc, t) => {
          if (!acc[t.utteranceId]) {
            acc[t.utteranceId] = [];
          }
          acc[t.utteranceId].push(t);
          return acc;
        },
        {} as Record<string, UtteranceTranslationModel[]>,
      );

      const formattedIntents = result.items.map((utterance) => {
        const availableLanguages = (translationsByUtteranceId[utterance.utteranceId] || []).reduce<
          Partial<Language>[]
        >((acc, t): Partial<Language>[] => {
          if (t.langId !== utterance.langId) {
            acc.push({
              id: t.langId,
              name: t.language?.name,
              code: t.language?.code,
            });
          }
          return acc;
        }, []);

        return {
          ...utterance,
          availableLanguages,
        };
      });

      res.json(
        successResponse({
          ...result,
          items: formattedIntents,
        }),
      );
    } catch (error) {
      logger.error("Error fetching utterance translations:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch utterance translations",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/utterance-translations/{id}:
   *   get:
   *     summary: Get utterance translation by ID
   *     tags: [Intent Utterance Translations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., utterance,language)
   *     responses:
   *       200:
   *         description: Utterance translation object
   */
  public getById = async (
    req: Request<UuidParams, any, any, IncludeQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const { include } = req.query;

      const includeAssociations = parseIncludeQuery(include, this.models);

      const intentUtteranceTranslation = await this.models.UtteranceTranslation.findOne({
        where: { id },
        include: includeAssociations,
      });

      if (!intentUtteranceTranslation) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Utterance translation not found" }));
        return;
      }

      const responseData = intentUtteranceTranslation.toJSON();

      res.json(
        successResponse({
          ...responseData,
          intentItem: intentUtteranceTranslation?.utterance?.intentItem,
        }),
      );
    } catch (error) {
      logger.error(`Error fetching utterance translation ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error: error as Error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch utterance translation",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/utterance/{utteranceId}/translations:
   *   get:
   *     summary: Get all translations for a given utterance ID
   *     tags: [Intent Utterance Translations]
   *     parameters:
   *       - in: path
   *         name: utteranceId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: List of utterance translations for the given utterance ID
   */
  public getTranslationsByUtteranceId = async (
    req: Request<UtteranceIdParam, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { utteranceId } = req.params;

      const translations = await this.models.UtteranceTranslation.findAll({
        where: { utteranceId },
      });

      if (!translations || translations.length === 0) {
        res.status(404).json(
          errorResponse({
            code: "NOT_FOUND",
            message: "No translations found for this utterance ID",
          }),
        );
        return;
      }

      res.json(successResponse(translations));
    } catch (error) {
      logger.error(`Error fetching translations for utterance ${req.params.utteranceId}:`, error);
      res.status(500).json(
        errorResponse({
          error: error as Error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch utterance translations",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/utterance/{utteranceId}/lang/{langId}/translation:
   *   get:
   *     summary: Get a specific translation for a given utterance ID and language ID
   *     tags: [Intent Utterance Translations]
   *     parameters:
   *       - in: path
   *         name: utteranceId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *       - in: path
   *         name: langId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       200:
   *         description: Utterance translation object for the given utterance ID and language ID
   */
  public getTranslationByUtteranceIdAndLangId = async (
    req: Request<UtteranceTranslationByLangParam, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { utteranceId, langId } = req.params;
      const translation = await this.models.UtteranceTranslation.findOne({
        where: { utteranceId, langId },
      });

      if (!translation) {
        res.status(404).json(
          errorResponse({
            code: "NOT_FOUND",
            message: "No translation found for this utterance ID and language ID",
          }),
        );
        return;
      }

      res.json(successResponse(translation));
    } catch (error) {
      logger.error(
        `Error fetching translation for utterance ${req.params.utteranceId} and language ${req.params.langId}:`,
        error,
      );
      res.status(500).json(
        errorResponse({
          error: error as Error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch utterance translation",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/utterance-translations/{id}:
   *   put:
   *     summary: Update an utterance translation
   *     tags: [Intent Utterance Translations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UtteranceTranslation'
   *     responses:
   *       200:
   *         description: Utterance translation updated successfully
   */
  public update = async (
    req: Request<UuidParams, any, UpdateUtteranceTranslationRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const [updated] = await this.models.UtteranceTranslation.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id },
        },
      );

      if (!updated) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Utterance translation not found" }));
        return;
      }

      const intentUtteranceTranslation = await this.models.UtteranceTranslation.findByPk(id);
      logger.info(`Utterance translation updated: ${id}`);

      res.json(successResponse(intentUtteranceTranslation));
    } catch (error) {
      logger.error(`Error updating utterance translation ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/utterance-translations/{id}:
   *   delete:
   *     summary: Delete an utterance translation
   *     tags: [Intent Utterance Translations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Utterance translation deleted successfully
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;

      const translationToDelete = await this.models.UtteranceTranslation.findByPk(id);
      if (!translationToDelete) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Utterance translation not found" }));
        return;
      }

      const utteranceId = translationToDelete.utteranceId;

      const deleted = await this.models.UtteranceTranslation.destroy({ where: { id } });

      if (!deleted) {
        res
          .status(404)
          .json(errorResponse({ code: "NOT_FOUND", message: "Utterance translation not found" }));
        return;
      }

      logger.info(`Utterance translation deleted: ${id}`);

      // Check if this was the last translation for its parent utterance
      const remainingTranslations = await this.models.UtteranceTranslation.count({
        where: { utteranceId },
      });

      if (remainingTranslations === 0) {
        // If no more translations, delete the parent IntentUtterance
        await this.models.IntentUtterance.destroy({ where: { id: utteranceId } });
        logger.info(
          `Parent IntentUtterance group deleted as it has no more translations: ${utteranceId}`,
        );
      }

      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting utterance translation ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to delete utterance translation",
        }),
      );
    }
  };
}
