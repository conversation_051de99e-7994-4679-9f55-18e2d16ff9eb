/**
 * Flow Service
 *
 * Handles CRUD operations for flows and flow management.
 */

import { Op } from "sequelize";
import { CreateFlowRequest } from "../types";
import { v4 as uuidv4 } from "uuid";
import { logger } from "@neuratalk/common";
import { DatabaseConnection, FlowAttributes as Flow, FlowType, Models } from "@neuratalk/bot-store";
import { CreateFlowPayload, ExtendedCreateFlowType } from "../schemas";
import { getStudioAppsService } from "api_gw";
import Transaction from "sequelize/types/transaction";
import crypto from "crypto";

type UpdateFlowRequest = any; //TODO: need to add proper type

export class FlowService {
  private studioAppsService: any;

  constructor(private db: DatabaseConnection) {
    this.studioAppsService = getStudioAppsService();
  }

  private createFlowHelper = async (
    request: CreateFlowPayload,
    userId: string,
    transaction: Transaction,
  ): Promise<Flow> => {
    const app = await this.studioAppsService.createApp({
      id: uuidv4(),
      name: `Untitled ${uuidv4().slice(0, 4)}`,
      desc: "A new app",
      status: 0,
      appData: {},
      ngage_id: uuidv4().slice(0, 4),
      owner: 0,
      alignment: "vertical",
      OTC: 10,
      MRC: 100,
      freeNodeExec: 10,
      channels: 0,
      nodes: 2,
      createdBy: userId,
      modifiedBy: userId,
    });

    const flow = await this.db.models.Flow.create(
      {
        ...request,
        appId: app.id,
        createdBy: userId,
        updatedBy: userId,
      },
      { transaction },
    );

    logger.info(`Flow created: ${flow.id} - ${flow.name}`);
    return flow.toJSON();
  };

  async createFlow(
    request: ExtendedCreateFlowType,
    userId: string,
    transaction?: Transaction,
  ): Promise<Flow> {
    try {
      if (transaction) {
        return await this.createFlowHelper(request, userId, transaction);
      }
      const newFlow = await this.db.transaction(async (transaction) => {
        return this.createFlowHelper(request, userId, transaction);
      });
      return newFlow;
    } catch (error) {
      logger.error("Error creating flow:", error);
      throw error;
    }
  }

  async getFlowById(id: string): Promise<Flow | null> {
    try {
      const flow = await this.db.models.Flow.findByPk(id);
      return flow ? flow.toJSON() : null;
    } catch (error) {
      logger.error(`Error getting flow ${id}:`, error);
      throw error;
    }
  }

  async updateFlow(id: string, request: UpdateFlowRequest, userId?: string): Promise<Flow | null> {
    try {
      const flow = await this.db.models.Flow.findByPk(id);
      if (!flow) {
        return null;
      }

      const updateData: Partial<UpdateFlowRequest & { updatedBy?: string }> = {
        ...request,
      };
      if (userId) {
        updateData.updatedBy = userId;
      }

      await flow.update(updateData);

      logger.info(`Flow updated: ${flow.id} - ${flow.name}`);
      return flow.toJSON();
    } catch (error) {
      logger.error(`Error updating flow ${id}:`, error);
      throw error;
    }
  }

  async deleteFlow(id: string): Promise<boolean> {
    try {
      const result = await this.db.models.Flow.destroy({ where: { id, type: FlowType.CUSTOM } });
      const deleted = result > 0;

      if (deleted) {
        logger.info(`Flow deleted: ${id}`);
      }

      return deleted;
    } catch (error) {
      logger.error(`Error deleting flow ${id}:`, error);
      throw error;
    }
  }

  async getFlowsByBotId(botId: string): Promise<Flow[]> {
    try {
      const flows = await this.db.models.Flow.findAll({ where: { botId } });
      return flows.map((flow) => {
        const flowJson = flow.toJSON();
        return {
          ...flowJson,
        };
      });
    } catch (error) {
      logger.error(`Error getting all flows for bot ${botId}:`, error);
      throw error;
    }
  }

  async getFlowsByBot(
    botId: string,
    page: number = 1,
    limit: number = 20,
    search?: string,
    isActive?: boolean,
  ): Promise<{ flows: Flow[]; total: number }> {
    try {
      const offset = (page - 1) * limit;
      const where: any = { botId };

      if (search) {
        where[Op.or] = [
          { name: { [Op.like]: `%${search}%` } },
          { description: { [Op.like]: `%${search}%` } },
        ];
      }

      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      const { count, rows } = await this.db.models.Flow.findAndCountAll({
        where,
        limit,
        offset,
        order: [["createdAt", "DESC"]],
      });

      return { flows: rows.map((r) => r.toJSON()), total: count };
    } catch (error) {
      logger.error(`Error getting flows for bot ${botId}:`, error);
      throw error;
    }
  }

  async cloneFlow(originalFlowId: string, userId: string): Promise<Flow | null> {
    const transaction = await this.db.sequelize.transaction();
    try {
      const originalFlow = await this.db.models.Flow.findByPk(originalFlowId, { transaction });

      if (!originalFlow) {
        await transaction.rollback();
        return null;
      }

      const cloneAppParams = {
        params: {
          appId: originalFlow.appId,
        },
      };
      const originalApp = await this.studioAppsService.exportApp(cloneAppParams, null);
      const reqBody = {
        ...originalApp,
        id: uuidv4(),
        name: originalApp.name + "_" + crypto.randomUUID().slice(0, 4),
        ngage_id: crypto.randomUUID().slice(0, 4),
        type: "Live",
      };
      const newApp = await this.studioAppsService.createApp(reqBody);

      // 2. Create the new flow record
      const originalFlowData = originalFlow.toJSON();
      const newFlowData = {
        ...originalFlowData,
        id: uuidv4(),
        name: `${originalFlow.name} - Clone`,
        appId: newApp.id,
        createdBy: userId,
        updatedBy: userId,
      };
      delete (newFlowData as any).createdAt;
      delete (newFlowData as any).updatedAt;

      const newFlow = await this.db.models.Flow.create(newFlowData, { transaction });

      await transaction.commit();
      logger.info(`Flow ${originalFlowId} successfully cloned to new flow ${newFlow.id}`);
      return newFlow.toJSON();
    } catch (error) {
      await transaction.rollback();
      logger.error(`Error cloning flow ${originalFlowId}:`, error);
      throw error;
    }
  }
  // async bulkCreateFlows(
  //   requests: CreateFlowRequest[],
  //   userId?: string,
  // ): Promise<{
  //   created: Flow[];
  //   failed: Array<{ request: CreateFlowRequest; error: string }>;
  // }> {
  //   const created: Flow[] = [];
  //   const failed: Array<{ request: CreateFlowRequest; error: string }> = [];

  //   for (const request of requests) {
  //     try {
  //       const flow = await this.createFlow(request, userId);
  //       created.push(flow);
  //     } catch (error) {
  //       failed.push({
  //         request,
  //         error: error instanceof Error ? error.message : "Unknown error",
  //       });
  //     }
  //   }

  //   logger.info(`Bulk flow creation: ${created.length} created, ${failed.length} failed`);

  //   return { created, failed };
  // }
}
