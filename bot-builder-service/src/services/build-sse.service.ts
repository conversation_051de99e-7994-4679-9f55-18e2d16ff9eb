import { Request, Response } from "express";
import { logger } from "@neuratalk/common";
import { TrainingJobStatus } from "@neuratalk/bot-store";

export interface BuildEvent {
  botId: string;
  jobId: string;
  status: TrainingJobStatus;
  timestamp: string;
  message?: string;
  modelUrl?: string;
  errorMessage?: string;
}

export class BuildSseService {
  private static instance: BuildSseService;
  private readonly clients: Map<string, Response> = new Map();

  private constructor() {
    logger.info("BuildSseService initialized as a singleton.");
  }

  public static getInstance(): BuildSseService {
    if (!BuildSseService.instance) {
      BuildSseService.instance = new BuildSseService();
    }
    return BuildSseService.instance;
  }

  public addClient(botId: string, req: Request, res: Response): void {
    if (this.clients.has(botId)) {
      logger.warn(`Existing SSE client for botId ${botId} will be replaced.`);
      // Close the old connection if it exists
      const oldRes = this.clients.get(botId);
      if (oldRes && !oldRes.headersSent) {
        oldRes.end();
      }
    }

    this.clients.set(botId, res);

    // Set SSE headers
    res.writeHead(200, {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    });

    // Send a heartbeat or initial message
    this.sendEvent(botId, {
      botId,
      jobId: "N/A",
      status: TrainingJobStatus.PENDING,
      timestamp: new Date().toISOString(),
      message: "Connected to build updates.",
    });

    // Handle client disconnect
    req.on("close", () => {
      this.removeClient(botId);
    });

    logger.debug(`SSE client added for botId ${botId}.`);
  }

  public removeClient(botId: string): void {
    this.clients.delete(botId);
    logger.debug(`SSE client for botId ${botId} disconnected.`);
  }

  public sendEvent(botId: string, event: BuildEvent): void {
    const client = this.clients.get(botId);
    if (client && !client.headersSent) {
      try {
        client.write(`data: ${JSON.stringify(event)}\n`);
        client.flush();
      } catch (error) {
        logger.error(`Error sending SSE event to botId ${botId}:`, error);
        this.removeClient(botId); // Remove client on error
      }
    } else if (client?.headersSent) {
      logger.warn(
        `Attempted to send SSE event to botId ${botId} but headers already sent. Client might be disconnected.`,
      );
      this.removeClient(botId);
    } else {
      logger.debug(`No active SSE client for botId ${botId} to send event.`);
    }
  }
}
