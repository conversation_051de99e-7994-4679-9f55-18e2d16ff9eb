/**
 * Bot Service
 *
 * Handles CRUD operations for bots, cloning, import/export, and related business logic.
 */

import {
  BuildBotResponse,
  BotExportData,
  ExportedFlow,
  ExportedIntent,
  ExportedFaqCategory,
} from "../types";
import { CreateBotRequest, UpdateBotRequest } from "../schemas/bot.schemas";
import { v4 as uuidv4 } from "uuid";
import { getPaginatedResults, logger } from "@neuratalk/common";
import {
  Bot,
  BotLanguageModel,
  BotSettings,
  BotStatus,
  DatabaseConnection,
  EntitiesModel,
  FaqCategoryModel,
  FaqCategoryType,
  FaqItemsModel,
  FaqTranslationModel,
  FlowModel,
  FlowType,
  IntentItemsModel,
  IntentUtteranceModel,
  UtteranceTranslationModel,
  TrainingJobStatus,
  Flow,
} from "@neuratalk/bot-store";
import { getStudioAppsService } from "api_gw"; // For handling legacy apps
import crypto from "crypto";
import { FlowService } from "./flow.service";

// Default bot settings
const defaultSettings: BotSettings = {
  nlu: {
    provider: "rasa",
    confidenceThreshold: 0.7,
    fallbackIntent: "nlu_fallback",
  },
  session: {
    ttlMinutes: 30,
    maxConcurrentSessions: 10,
    persistContext: true,
    enableSessionResumption: false,
  },
  messaging: {
    enableTypingIndicator: true,
    typingDelayMs: 1000,
    maxMessageLength: 4000,
    enableQuickReplies: true,
    enableRichMessages: true,
    defaultErrorMessage: "I apologize, but I encountered an error. Please try again.",
    defaultFallbackMessage: "I'm sorry, I didn't understand that. Could you please rephrase?",
  },
  execution: {
    maxExecutionTimeMs: 30000,
    maxLoopIterations: 100,
    enableAsyncOperations: true,
    asyncTimeoutMs: 30000,
    enableScriptExecution: true,
    scriptTimeoutMs: 100,
  },
  integrations: {
    analytics: { enabled: false },
    logging: {
      level: "info",
      enableChatHistory: true,
      retentionDays: 30,
    },
  },
  security: {
    enableRateLimit: true,
    rateLimitPerMinute: 60,
    enableInputValidation: true,
    allowedFileTypes: ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx"],
    maxFileSize: 10485760, // 10MB
    enableContentFilter: false,
  },
};

// Add default languages
const defaultLanguageCodes = ["en"];

const defaultFlowData = [
  {
    name: "Welcome",
    description: "Default flow",
  },
  {
    name: "Fallback",
    description: "Default flow",
  },
];

export class BotService {
  private studioAppsService;

  constructor(
    private flowService: FlowService,
    private db: DatabaseConnection,
  ) {
    this.studioAppsService = getStudioAppsService();
  }

  async createBot(request: CreateBotRequest, userId: string): Promise<Bot> {
    try {
      const settings = { ...defaultSettings, ...request.settings };

      const newBot = await this.db.transaction(async (transaction) => {
        const bot = await this.db.models.Bot.create(
          {
            name: request.name,
            description: request.description,
            status: BotStatus.DRAFT,
            settings,
            metadata: request.metadata || {},
            createdBy: userId,
            updatedBy: userId,
          },
          { transaction },
        );

        const languages = await this.db.models.Language.findAll({
          where: {
            code: defaultLanguageCodes,
          },
          transaction,
        });

        const botLanguages = languages.map((lang) => ({
          botId: bot.id,
          langId: lang.id,
          createdBy: userId,
          isDefault: lang.code === "en",
        }));

        await this.db.models.BotLanguage.bulkCreate(botLanguages, { transaction });

        await this.db.models.FaqCategory.create(
          {
            botId: bot.id,
            name: "Default",
            type: FaqCategoryType.DEFAULT,
            createdBy: userId,
            updatedBy: userId,
          },
          { transaction },
        );

        await Promise.all(
          defaultFlowData.map(async (flow) => {
            await this.flowService.createFlow(
              {
                name: flow.name,
                description: flow.description,
                type: FlowType.DEFAULT,
                botId: bot.id,
              },
              userId,
              transaction,
            );
          }),
        );
        return bot;
      });

      logger.info(`Bot created: ${newBot.id} - ${newBot.name}`);
      return newBot.toJSON();
    } catch (error) {
      logger.error("Error creating bot:", error);
      throw error;
    }
  }

  async getBotById(id: string): Promise<Bot | null> {
    try {
      const bot = await this.db.models.Bot.findByPk(id);
      return bot ? (bot.toJSON() as Bot) : null;
    } catch (error) {
      logger.error(`Error getting bot ${id}:`, error);
      throw error;
    }
  }

  async updateBot(id: string, request: UpdateBotRequest, userId: string): Promise<Bot | null> {
    try {
      const bot = await this.db.models.Bot.findByPk(id);
      if (!bot) {
        return null;
      }

      await bot.update({ ...request, updatedBy: userId });
      logger.info(`Bot updated: ${bot.id} - ${bot.name}`);
      return bot.toJSON() as Bot;
    } catch (error) {
      logger.error(`Error updating bot ${id}:`, error);
      throw error;
    }
  }

  async deleteBot(id: string): Promise<boolean> {
    try {
      // const result = await this.db.models.Bot.destroy({ where: { id } });
      const result = await this.db.models.Bot.destroy({ where: { id } });
      const deleted = result > 0;
      if (deleted) {
        logger.info(`Bot deleted: ${id}`);
      }
      return deleted;
    } catch (error) {
      logger.error(`Error deleting bot ${id}:`, error);
      throw error;
    }
  }

  async activateBot(id: string, userId: string): Promise<Bot | null> {
    return this.updateBotStatus(id, BotStatus.ACTIVE, userId);
  }

  async deactivateBot(id: string, userId: string): Promise<Bot | null> {
    return this.updateBotStatus(id, BotStatus.INACTIVE, userId);
  }

  private async updateBotStatus(
    id: string,
    status: BotStatus,
    userId: string,
  ): Promise<Bot | null> {
    try {
      const bot = await this.db.models.Bot.findByPk(id);
      if (!bot) {
        return null;
      }
      await bot.update({ status, updatedBy: userId });
      logger.info(`Bot status updated: ${bot.id} - ${status}`);
      return bot.toJSON();
    } catch (error) {
      logger.error(`Error updating bot status ${id}:`, error);
      throw error;
    }
  }

  async getChannelConfig(botId: string, channelType: string): Promise<any> {
    return {
      channelType,
      isActive: true,
      config: {
        webhookUrl: `https://api.example.com/webhooks/${channelType}`,
        apiKey: "mock-api-key",
      },
    };
  }

  async createChannelIntegration(botId: string, channelData: any): Promise<any> {
    return {
      id: "channel-" + Date.now(),
      botId,
      ...channelData,
      createdAt: new Date(),
    };
  }

  async updateChannelIntegration(botId: string, channelId: string, updateData: any): Promise<any> {
    return {
      id: channelId,
      botId,
      ...updateData,
      updatedAt: new Date(),
    };
  }

  async cloneBot(originalBotId: string, userId: string): Promise<Bot | null> {
    const transaction = await this.db.sequelize.transaction();
    try {
      const originalBotData = await this._getBotExportData(originalBotId, transaction);
      if (!originalBotData) return null;

      const newBot = await this._importBotData(originalBotData, userId, transaction, {
        isClone: true,
      });

      await transaction.commit();
      logger.info(`Bot ${originalBotId} successfully cloned to new bot ${newBot.id}`);
      return newBot;
    } catch (error) {
      await transaction.rollback();
      logger.error(`Error cloning bot ${originalBotId}:`, error);
      throw error;
    }
  }

  /**
   * Exports a bot's complete data into a JSON object.
   */
  async exportBotData(botId: string): Promise<BotExportData | null> {
    const botData = await this._getBotExportData(botId);
    if (!botData) return null;
    return botData;
  }

  async importBotData(data: BotExportData, userId: string): Promise<Bot> {
    const transaction = await this.db.sequelize.transaction();
    try {
      const newBot = await this._importBotData(data, userId, transaction);
      await transaction.commit();
      logger.info(`Bot successfully imported with new ID ${newBot.id}`);
      return newBot;
    } catch (error) {
      await transaction.rollback();
      logger.error(`Error importing bot:`, error);
      throw error;
    }
  }

  private async _getBotExportData(botId: string, transaction?: any): Promise<BotExportData | null> {
    const bot = await this.db.models.Bot.findByPk(botId, {
      attributes: { exclude: ["createdAt", "deletedAt"] },
      include: [
        { model: this.db.models.BotLanguage, as: "botLanguages" },
        { model: this.db.models.Flow, as: "flows" },
        {
          model: this.db.models.FaqCategory,
          as: "faqCategories",
          include: [
            {
              model: this.db.models.FaqItems,
              as: "faqItems",
              include: [{ model: this.db.models.FaqTranslation, as: "faqTranslations" }],
            },
          ],
        },
        {
          model: this.db.models.IntentItems,
          as: "intentItems",
          include: [
            { model: this.db.models.Entities, as: "entities" },
            {
              model: this.db.models.IntentUtterance,
              as: "intentUtterances",
              include: [
                { model: this.db.models.UtteranceTranslation, as: "utteranceTranslations" },
              ],
            },
          ],
        },
      ],
      transaction,
    });

    if (!bot) return null;

    const exportedFlows: ExportedFlow[] = await Promise.all(
      (bot.flows || []).map(async (flow: FlowModel) => {
        const appData = await this.studioAppsService.exportApp(flow.appId);
        return { flowData: flow.toJSON(), appData: appData || {} };
      }),
    );

    const exportedFaqCategories: ExportedFaqCategory[] = (bot.faqCategories || []).map(
      (cat: FaqCategoryModel) => ({
        category: cat.toJSON(),
        items: (cat.faqItems || []).map((item: FaqItemsModel) => ({
          item: item.toJSON(),
          translations: (item.faqTranslations || []).map((t: FaqTranslationModel) => t.toJSON()),
        })),
      }),
    );

    const exportedIntents: ExportedIntent[] = (bot.intentItems || []).map(
      (intent: IntentItemsModel) => ({
        intent: intent.toJSON(),
        entities: (intent.entities || []).map((e: EntitiesModel) => e.toJSON()),
        utterances: (intent.intentUtterances || []).map((utt: IntentUtteranceModel) => ({
          utterance: utt.toJSON(),
          translations: (utt.utteranceTranslations || []).map((t: UtteranceTranslationModel) =>
            t.toJSON(),
          ),
        })),
      }),
    );

    return {
      exportVersion: "1.0",
      exportedAt: new Date().toISOString(),
      bot: bot.toJSON(),
      languages: (bot.botLanguages || []).map((l: BotLanguageModel) => l.toJSON()),
      flows: exportedFlows,
      faqCategories: exportedFaqCategories,
      intents: exportedIntents,
    };
  }

  private async _importBotData(
    data: BotExportData,
    userId: string,
    transaction: any,
    options: { isClone?: boolean } = {},
  ): Promise<Bot> {
    const { bot, languages, flows, intents, faqCategories } = data;

    // 1. Create New Bot
    const newBotData = {
      ...bot,
      id: uuidv4(),
      name: options.isClone
        ? `${bot.name} - Clone`
        : `Imported - ${bot.name} ${uuidv4().slice(0, 4)}`,
      status: BotStatus.DRAFT,
      createdBy: userId,
      updatedBy: userId,
    };

    const newBot = await this.db.models.Bot.create(newBotData, { transaction });

    // 2. Create Bot Languages
    const newLanguages = languages.map((lang) => ({
      ...lang,
      id: uuidv4(),
      botId: newBot.id,
      createdBy: userId,
    }));
    await this.db.models.BotLanguage.bulkCreate(newLanguages, { transaction });

    // ID Maps for re-linking entities
    const flowIdMap = new Map<string, string>();

    // 3. Create Flows and Apps
    for (const exportedFlow of flows) {
      const { flowData, appData } = exportedFlow;
      const reqBody = {
        ...appData,
        id: uuidv4(),
        name: appData.name + "_" + crypto.randomUUID().slice(0, 4),
        ngage_id: appData.ngage_id + "_" + crypto.randomUUID().slice(0, 4),
        type: "Live",
      };
      const newApp = await this.studioAppsService.createApp(reqBody);

      const newFlowData = {
        ...flowData,
        id: uuidv4(),
        botId: newBot.id,
        appId: newApp.id,
        createdBy: userId,
        updatedBy: userId,
      };
      const newFlow = await this.db.models.Flow.create(newFlowData, { transaction });
      flowIdMap.set(flowData.id, newFlow.id);
    }

    // 4. Create FAQ Categories and Items
    for (const exportedCat of faqCategories) {
      const newCategory = await this.db.models.FaqCategory.create(
        {
          ...exportedCat.category,
          id: uuidv4(),
          botId: newBot.id,
          createdBy: userId,
          updatedBy: userId,
        },
        { transaction },
      );
      for (const exportedItem of exportedCat.items) {
        const newItem = await this.db.models.FaqItems.create(
          {
            ...exportedItem.item,
            id: uuidv4(),
            botId: newBot.id,
            categoryId: newCategory.id,
            flowId: exportedItem.item.flowId ? flowIdMap.get(exportedItem.item.flowId) : undefined,
            createdBy: userId,
            updatedBy: userId,
          },
          { transaction },
        );
        const newTranslations = exportedItem.translations.map((t) => ({
          ...t,
          id: uuidv4(),
          faqId: newItem.id,
          createdBy: userId,
          updatedBy: userId,
        }));
        await this.db.models.FaqTranslation.bulkCreate(newTranslations, { transaction });
      }
    }

    // 5. Create Intents, Utterances, Entities
    for (const exportedIntent of intents) {
      const newIntent = await this.db.models.IntentItems.create(
        {
          ...exportedIntent.intent,
          id: uuidv4(),
          botId: newBot.id,
          flowId: exportedIntent.intent.flowId
            ? flowIdMap.get(exportedIntent.intent.flowId)
            : undefined,
          createdBy: userId,
          updatedBy: userId,
        },
        { transaction },
      );

      const newEntities = exportedIntent.entities.map((e) => ({
        ...e,
        id: uuidv4(),
        botId: newBot.id,
        intentId: newIntent.id,
        createdBy: userId,
        updatedBy: userId,
      }));
      await this.db.models.Entities.bulkCreate(newEntities, { transaction });

      for (const exportedUtt of exportedIntent.utterances) {
        const newUtterance = await this.db.models.IntentUtterance.create(
          { ...exportedUtt.utterance, id: uuidv4(), intentId: newIntent.id, createdBy: userId },
          { transaction },
        );
        const newTranslations = exportedUtt.translations.map((t) => ({
          ...t,
          id: uuidv4(),
          utteranceId: newUtterance.id,
          createdBy: userId,
          updatedBy: userId,
        }));
        await this.db.models.UtteranceTranslation.bulkCreate(newTranslations, { transaction });
      }
    }

    return newBot.toJSON();
  }

  async publishBot(botId: string): Promise<{ message: string }> {
    try {
      const bot = await this.db.models.Bot.findByPk(botId, {
        include: [
          {
            model: this.db.models.TrainingJob,
            as: "trainingJobs",
            where: {
              status: TrainingJobStatus.COMPLETED,
            },
            order: [["createdAt", "DESC"]],
            limit: 1,
          },
        ],
      });

      if (!bot) {
        throw new Error("Bot not found.");
      }

      const mostRecentTrainingModel = bot.trainingJobs?.[0];

      if (!mostRecentTrainingModel?.modelUrl) {
        throw new Error("No build model found for this bot.");
      }

      // Get all flows for this bot
      const flows = await this.flowService.getFlowsByBotId(botId);
      const appFlowMap = flows.reduce(
        (acc, flow) => {
          acc[flow.appId] = flow;
          return acc;
        },
        {} as Record<string, Flow>,
      );

      const appIds = Object.keys(appFlowMap);

      if (appIds.length > 0) {
        const publishResult = await this.studioAppsService.bulkPublishApps(appIds);

        if (publishResult.failed && publishResult.failed.length > 0) {
          const errorDetails = publishResult.failed
            .map((f: any) => {
              const flowName = appFlowMap[f?.appId]?.name;

              return `${flowName ? "Flow" : ""} ${flowName ?? bot.name}: ${f.reason}`;
            })
            .join(";\n");
          throw new Error(`Failed to publish Bot: \n${errorDetails}`);
        }

        logger.info(`Published ${publishResult.published.length} apps for bot ${botId}`);
      }

      await bot.update({ publishedModelId: mostRecentTrainingModel.id, status: BotStatus.ACTIVE });

      logger.info(`Bot ${botId} published with model ID: ${mostRecentTrainingModel.id}`);
      return {
        message: `Bot ${botId} published successfully with model ID ${mostRecentTrainingModel.id}.`,
      };
    } catch (error) {
      logger.error(`Error publishing bot ${botId}:`, error);
      throw error;
    }
  }
}
