import { z } from "zod";
import { UuidSchema } from "@neuratalk/common";

export const CreateLanguageSchema = z
  .object({
    name: z.string().min(1).max(255),
    code: z.string().min(1).max(10),
    nativeName: z.string().min(1).max(255),
  })
  .strict();

export const UpdateLanguageSchema = CreateLanguageSchema.partial().strict();

export const CreateBotLanguageSchema = z
  .object({
    langId: UuidSchema,
  })
  .strict();

export const UpdateBotLanguageSchema = z.object({}).strict();

export type CreateLanguageRequest = z.infer<typeof CreateLanguageSchema>;
export type UpdateLanguageRequest = z.infer<typeof UpdateLanguageSchema>;
export const BulkCreateBotLanguageSchema = z
  .object({
    ids: z.array(UuidSchema).min(1, "At least one ID is required for bulk operation"),
  })
  .strict();
export const BulkDeleteBotLanguageSchema = BulkCreateBotLanguageSchema;

export type CreateBotLanguageRequest = z.infer<typeof CreateBotLanguageSchema>;
export type UpdateBotLanguageRequest = z.infer<typeof UpdateBotLanguageSchema>;
export type BulkCreateBotLanguageRequest = z.infer<typeof BulkCreateBotLanguageSchema>;
export type BulkDeleteBotLanguageRequest = z.infer<typeof BulkDeleteBotLanguageSchema>;
