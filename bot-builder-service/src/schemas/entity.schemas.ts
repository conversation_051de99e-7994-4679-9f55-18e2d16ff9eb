import { z } from "zod";
import { UuidSchema, createNameSchema } from "@neuratalk/common";
import { EntityType } from "@neuratalk/bot-store";

// Entity schemas
export const CreateEntitySchema = z
  .object({
    botId: UuidSchema,
    intentId: UuidSchema.optional(),
    name: createNameSchema(100),
    type: z.nativeEnum(EntityType),
    metadata: z.record(z.any()).optional(),
  })
  .strict();

export const UpdateEntitySchema = CreateEntitySchema.omit({ botId: true, intentId: true })
  .partial()
  .strict();

// Type extraction
export type CreateEntityRequest = z.infer<typeof CreateEntitySchema>;
export type UpdateEntityRequest = z.infer<typeof UpdateEntitySchema>;
