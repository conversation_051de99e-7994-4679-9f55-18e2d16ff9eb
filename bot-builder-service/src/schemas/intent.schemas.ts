import { z } from "zod";
import { UuidSchema, createNameSchema } from "@neuratalk/common";

// Intent Item schemas
export const CreateIntentItemSchema = z
  .object({
    botId: UuidSchema,
    name: createNameSchema(32),
  })
  .strict();

export const UpdateIntentItemSchema = CreateIntentItemSchema.omit({ botId: true })
  .partial()
  .strict();

export const AssignFlowToIntentSchema = z
  .object({
    flowId: UuidSchema,
    intentId: UuidSchema,
  })
  .strict();

// Intent Utterance Translation schemas
export const CreateUtteranceTranslationSchema = z
  .object({
    utteranceId: UuidSchema.optional(),
    text: z.string().min(1),
    entities: z.record(z.any()).optional(), //TODO: add a more specific type for entities
  })
  .strict();

export const UpdateUtteranceTranslationSchema = CreateUtteranceTranslationSchema.omit({
  utteranceId: true,
})
  .partial()
  .strict();

export const UtteranceTranslationParamSchema = z
  .object({
    intentId: UuidSchema,
    langId: UuidSchema,
  })
  .strict();

export const UtteranceIdParamSchema = z
  .object({
    utteranceId: UuidSchema,
  })
  .strict();

export const UtteranceTranslationByLangParamSchema = z
  .object({
    utteranceId: UuidSchema,
    langId: UuidSchema,
  })
  .strict();

// Type extraction
export type CreateIntentItemRequest = z.infer<typeof CreateIntentItemSchema>;
export type UpdateIntentItemRequest = z.infer<typeof UpdateIntentItemSchema>;
export type AssignFlowToIntentRequest = z.infer<typeof AssignFlowToIntentSchema>;
export type CreateUtteranceTranslationRequest = z.infer<typeof CreateUtteranceTranslationSchema>;
export type UpdateUtteranceTranslationRequest = z.infer<typeof UpdateUtteranceTranslationSchema>;
export type UtteranceTranslationParam = z.infer<typeof UtteranceTranslationParamSchema>;
export type UtteranceIdParam = z.infer<typeof UtteranceIdParamSchema>;
export type UtteranceTranslationByLangParam = z.infer<typeof UtteranceTranslationByLangParamSchema>;
