import { Request, Response, NextFunction } from "express";
import { errorResponse } from "@neuratalk/common";
import { PermissionKeys } from "../types";

export const authorizeMiddleware = (requiredPermissions: PermissionKeys[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        res.status(403).json(
          errorResponse({
            code: "AUTH_ERROR",
            message: "Missing or invalid Authorization header",
          }),
        );
        return;
      }

      const userPermissions = req.user.permissions || [];

      if (!requiredPermissions.length) {
        return next(); // No specific permission required, allow any authenticated user
      }

      const hasAllPermissions = requiredPermissions.every((permission) =>
        userPermissions.includes(permission),
      );

      if (!hasAllPermissions) {
        res.status(403).json(
          errorResponse({
            code: "AUTH_ERROR",
            message: "Forbidden",
          }),
        );
        return;
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};
