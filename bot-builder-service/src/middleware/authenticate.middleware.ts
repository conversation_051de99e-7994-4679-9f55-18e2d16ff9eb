/* eslint-disable @typescript-eslint/no-namespace */
import { Request, Response, NextFunction } from "express";
import { errorResponse } from "@neuratalk/common";
import { ADMIN_USER_ID } from "../config";
import jwt from "jsonwebtoken";
import { DecodedTokenPayload } from "../types";

declare global {
  namespace Express {
    interface Request {
      user: {
        id: string;
        [key: string]: any;
      };
    }
  }
}

export function authenticateMiddleware(req: Request, res: Response, next: NextFunction): void {
  try {
    const authHeader = req.headers["authorization"];

    if (!authHeader?.startsWith("Bearer ")) {
      res.status(401).json(
        errorResponse({
          code: "AUTH_ERROR",
          message: "Missing or invalid Authorization header",
        }),
      );
      return;
    }

    const token = authHeader.split(" ")[1];

    const decoded = jwt.decode(token) as DecodedTokenPayload;

    if (!decoded) {
      res.status(401).json(
        errorResponse({
          code: "AUTH_ERROR",
          message: "Invalid token",
        }),
      );
      return;
    }

    const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds
    if (decoded.exp && decoded.exp < currentTime) {
      res.status(401).json(
        errorResponse({
          code: "AUTH_ERROR",
          message: "Token of the user has expired",
        }),
      );
      return;
    }

    req.user = {
      id: decoded.sub || ADMIN_USER_ID,
      name: decoded.name,
      username: decoded.preferred_username,
      permissions: decoded.scope?.split(" ") ?? [],
    };

    next();
  } catch (error) {
    res.status(500).json(
      errorResponse({
        code: "AUTH_ERROR",
        message: "Authentication failed",
        error,
      }),
    );
  }
}
