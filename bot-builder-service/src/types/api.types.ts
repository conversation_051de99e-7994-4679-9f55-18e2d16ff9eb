/**
 * API Request and Response Types
 *
 * These types define the structure of API requests and responses across all services.
 */

import { PaginatedResponse, PaginationParams } from "@neuratalk/common";
import { CreateFlowRequest } from "./bot.types";
import { FlowAttributes as Flow, BotAttributes as Bot } from "@neuratalk/bot-store";

type Node = any;
// --- Bot Builder Service APIs ---

export interface CreateBotResponse {
  bot: Bot;
}

export interface GetBotsRequest extends PaginationParams {
  search?: string;
  status?: "active" | "inactive";
}

export interface GetBotsResponse extends PaginatedResponse<Bot> {}

export interface CreateFlowResponse {
  flow: Flow;
}

export interface UpdateFlowRequest {
  name?: string;
  description?: string;
  nodes?: Record<string, Node>;
  entryNodeId?: string;
  isActive?: boolean;
  metadata?: Record<string, any>;
}

export interface GetFlowsRequest extends PaginationParams {
  botId: string;
  search?: string;
  isActive?: boolean;
}

export interface GetFlowsResponse extends PaginatedResponse<Flow> {}

export interface BulkCreateFlowsRequest {
  botId: string;
  flows: CreateFlowRequest[];
}

export interface BulkCreateFlowsResponse {
  created: Flow[];
  failed: Array<{
    request: CreateFlowRequest;
    error: string;
  }>;
}

export interface DecodedTokenPayload {
  exp: number;
  iat: number;
  jti: string;
  iss: string;
  sub: string;
  typ: "Bearer";
  azp: string;
  session_state: string;
  realm_access: {
    roles: string[];
  };
  scope: string; // space-separated string of permissions
  sid: string;
  email_verified: boolean;
  groupId: string[];
  name: string;
  groups: string[];
  preferred_username: string;
  given_name: string;
  email: string;
}

export enum PermissionKeys {
  CREATE_NEURATALK = "neuratalk_c",
  UPDATE_NEURATALK = "neuratalk_e",
  DELETE_NEURATALK = "neuratalk_d",
  READ_NEURATALK = "neuratalk_r",
}

export enum TrainingResultStatus {
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}
