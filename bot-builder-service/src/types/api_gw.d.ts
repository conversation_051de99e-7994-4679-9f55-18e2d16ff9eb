import { Request, Response, NextFunction } from "express";
import {
  BotAttributes,
  FlowAttributes,
  FaqCategoryAttributes,
  FaqItemsAttributes,
  FaqTranslationAttributes,
  IntentItemsAttributes,
  IntentUtteranceAttributes,
  UtteranceTranslationAttributes,
  EntitiesAttributes,
  BotLanguageAttributes,
  Models,
  DatabaseConnection,
  BotStatus,
  FaqCategoryType,
  FlowType,
  TrainingJobStatus,
} from "@neuratalk/bot-store";

// Re-exporting types from @neuratalk/bot-store that are used in api_gw's internal logic
export {
  BotAttributes,
  FlowAttributes,
  FaqCategoryAttributes,
  FaqItemsAttributes,
  FaqTranslationAttributes,
  IntentItemsAttributes,
  IntentUtteranceAttributes,
  UtteranceTranslationAttributes,
  EntitiesAttributes,
  BotLanguageAttributes,
  Models,
  DatabaseConnection,
  BotStatus,
  FaqCategoryType,
  FlowType,
  TrainingJobStatus,
};

// Define types for the `req.invoker` object, which comes from the JWT decoding
declare global {
  namespace Express {
    interface Request {
      invoker: {
        id: string;
        sub: string; // Assuming 'sub' is the ngage_id
        role: string; // e.g., "app-developer", "it-admin"
        given_name?: string;
        realm_access?: { roles: string[] };
        groupId?: string[];
        [key: string]: any;
      };
      locale?: string; // Assuming locale might be added to request
    }
  }
}

// --- apps_proxy/apps_impl.js exports ---
export declare class AppsImpl {
  listAppVersions(req: Request, res: Response): Promise<any>;
  getAppSnapshot(req: Request, res: Response): Promise<any>;
  updateAppSnapshot(req: Request, res: Response): Promise<any>;
  getApps(req: Request, res: Response): Promise<void>;
  getAppsForAdmin(req: Request, res: Response): Promise<void>;
  getAppsForPlatform(req: Request, res: Response): Promise<void>;
  getTriggerEndpoints(req: Request, res: Response): Promise<void>;
  getAllApps(req: Request, res: Response): Promise<void>;
  getAppInfo(req: Request, res: Response): Promise<void>;
  checkForDuplicateName(req: Request, res: Response, next: NextFunction): Promise<void>;
  createAppInfo(req: Request, res: Response): Promise<any>;
  createAppWithAI(req: Request, res: Response): Promise<void>;
  getQueries(req: Request, res: Response): Promise<void>;
  updateAppInfo(req: Request, res: Response): Promise<void>;
  updateOTCAndMRC(req: Request, res: Response): Promise<void>;
  cloneAppTemplate(req: Request, res: Response): Promise<void>;
  cloneApp(req: Request, res: Response): Promise<void>;
  purgeAppInfo(req: Request, res: Response): Promise<any>;
  takeAppSnapshot(req: Request, res: Response): Promise<void>;
  getAppTemplates(req: Request, res: Response): Promise<void>;
  getAppTemplatesByType(req: Request, res: Response): Promise<void>;
  getAppTemplatesType(req: Request, res: Response): Promise<void>;
  downloadAppLocale(req: Request, res: Response): Promise<void>;
  listAppLocale(req: Request, res: Response): Promise<void>;
  deleteAppsLocale(req: Request, res: Response): Promise<void>;
  getCommentsPerApp(req: Request, res: Response): Promise<void>;
  copyAppUrl(req: Request, res: Response): Promise<void>;
  exportApp(appId: string): Promise<any>;
  importApp(req: Request, res: Response): Promise<void>;
  compressedApp(req: Request, res: Response): Promise<void>;
  publishApp(appId: string): Promise<{ success: boolean; error?: string; errors?: any[] }>;
  getGlobalContextAutocompleteSchema(req: Request, res: Response): Promise<void>;
  bulkPublishApps(
    appIds: string[],
  ): Promise<{
    published: { appId: string; version: string }[];
    failed: { appId: string; reason: string; errors?: any[] }[];
  }>;
}

// --- apps_proxy/session.js exports ---
export declare class SessionManager {
  createOrGet(jwToken: string, deviceRef: string): Promise<string>;
  getSession(userId: string, deviceRef?: string): Promise<any>;
  isValid(token: string): boolean;
  setSession(userId: string, deviceRef: string, sessionData: any): Promise<boolean>;
  delete(token: string, deviceRef: string): Promise<any>;
  check4Blacklist(req: Request, res: Response, next: NextFunction): Promise<void>;
  add2Blacklist(token: string, requestPath: string): Promise<boolean>;
}

// --- app_validation/lib/app_validator.js exports ---
export declare class AppValidator {
  validate(req: Request, res: Response): Promise<void>;
  validateGetApps(req: Request, res: Response, next: NextFunction): void;
  validateCreateAppInfo(req: Request, res: Response, next: NextFunction): void;
  validateUpdateAppInfo(req: Request, res: Response, next: NextFunction): void;
  validateAssignApp(req: Request, res: Response, next: NextFunction): void;
  validateCloneAppTemplate(req: Request, res: Response, next: NextFunction): void;
  validateCloneApp(req: Request, res: Response, next: NextFunction): void;
  getModuleErrors(appData: any): Promise<any>;
  validateUploadAppLocaleInfo(req: Request, res: Response, next: NextFunction): boolean;
  validateCreateUssdServiceInfo(req: Request, res: Response, next: NextFunction): void;
  validateUpdateUssdServiceInfo(req: Request, res: Response, next: NextFunction): void;
  validateCreateUssdConfigInfo(req: Request, res: Response, next: NextFunction): void;
  validateUpdateUssdConfigInfo(req: Request, res: Response, next: NextFunction): void;
}

// --- Combined exports for getStudioAppsService ---
export declare function initializeApiGw(isServer?: boolean): Promise<any>;
export declare function getStudioAppsService(): AppsImpl &
  SessionManager &
  AppValidator & {
    // Assuming app_store exports a default object or a class instance
    // For now, let's keep it as 'any' or add specific types if needed later.
    // Based on the usage in apps_impl.js, it seems to be an object with methods like listApps, findApp, etc.
    // For now, I'll represent it as a generic object.
    AppStore: any; // Placeholder for app_store exports
  };
