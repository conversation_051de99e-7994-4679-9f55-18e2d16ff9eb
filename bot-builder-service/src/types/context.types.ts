import { DatabaseConnection } from "@neuratalk/bot-store";
import { BotService } from "../services/bot.service";
import { FlowService } from "../services/flow.service";
import { BuildService } from "../services/build.service";
import { BuildSseService } from "../services/build-sse.service";

export interface AppContext {
  db: DatabaseConnection;
  botService: BotService;
  flowService: FlowService;
  buildService: BuildService;
  buildSseService: BuildSseService;
}