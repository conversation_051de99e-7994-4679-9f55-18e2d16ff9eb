import { ApiResponse } from "../types/api.types";

export const successResponse = <T>(data: T): ApiResponse<T> => {
  return {
    success: true,
    data,
    timestamp: new Date(),
  };
};

interface SequelizeErrorWithErrors {
  errors?: Array<{ message: string; [key: string]: any }>;
}
function isSequelizeErrorWithErrors(error: unknown): error is SequelizeErrorWithErrors {
  const err = error as SequelizeErrorWithErrors;

  return (
    typeof error === "object" &&
    error !== null &&
    Array.isArray(err.errors) &&
    err.errors.length > 0 &&
    typeof err.errors[0] === "object" &&
    err.errors[0] !== null &&
    typeof err.errors[0].message === "string"
  );
}

export const errorResponse = ({
  error,
  code,
  message = "An unknown error occurred",
  details,
}: {
  error?: unknown; // Made optional
  code: string;
  message?: string;
  details?: any;
}): ApiResponse<any> => {
  let errorMessage: string | undefined = message;

  if (isSequelizeErrorWithErrors(error)) {
    errorMessage = error.errors?.[0].message;
  } else if (error instanceof Error) {
    errorMessage = error.message;
  }

  return {
    success: false,
    error: {
      code,
      message: errorMessage,
      ...(details && { details }),
    },
    timestamp: new Date(),
  };
};
