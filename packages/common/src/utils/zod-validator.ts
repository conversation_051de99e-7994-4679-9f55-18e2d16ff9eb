import { Request, Response, NextFunction } from "express";
import { z } from "zod";
import { ApiResponse } from "../types/api.types";

export function validateBody(schema: z.ZodSchema) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const result = schema.safeParse(req.body);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "Invalid request body",
            details: result.error.errors,
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }
      req.body = result.data;
      next();
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Validation failed",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  };
}

export function validateParams(schema: z.ZodSchema) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const result = schema.safeParse(req.params);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "Invalid request parameters",
            details: result.error.errors,
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }
      req.params = result.data;
      next();
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Validation failed",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  };
}

export function validateQuery(schema: z.ZodSchema) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const result = schema.safeParse(req.query);
      if (!result.success) {
        res.status(400).json({
          success: false,
          error: {
            code: "VALIDATION_ERROR",
            message: "Invalid query parameters",
            details: result.error.errors,
          },
          timestamp: new Date(),
        } as ApiResponse);
        return;
      }
      req.query = result.data;
      next();
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Validation failed",
        },
        timestamp: new Date(),
      } as ApiResponse);
    }
  };
}
