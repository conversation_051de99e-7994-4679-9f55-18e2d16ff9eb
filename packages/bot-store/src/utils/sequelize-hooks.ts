import { Model, Op, Transaction } from "sequelize";

interface UniqueConstraintOptions {
  fields: string[]; // Fields that must be unique (e.g., ['name'])
  scope?: string[]; // Fields that define the scope for uniqueness (e.g., ['botId'])
  errorMessage: string; // Custom error message
}

/**
 * Creates a beforeValidate hook function to enforce unique constraints for paranoid models.
 * This utility is designed for MySQL, where partial unique indexes are not natively supported,
 * and works by checking for active (non-deleted) records with the same unique fields.
 *
 * @param model The Sequelize model class (e.g., BotModel).
 * @param options Configuration for the unique constraint.
 * @returns A beforeValidate hook function.
 */
export function createUniqueConstraintHook<T extends Model>(
  model: new () => T, // Constructor type for the model
  options: UniqueConstraintOptions
) {
  return async (instance: T, hookOptions: { transaction?: Transaction; isNewRecord: boolean }) => {
    const { fields, scope = [], errorMessage } = options;

    // Construct the WHERE clause for the uniqueness check
    const whereClause: { [key: string]: any } = {
      deletedAt: { [Op.is]: null }, // Always check against active records
    };

    // Add unique fields to the where clause
    for (const field of fields) {
      const value = (instance as any)[field];
      if (value === undefined) {
        // If a unique field is not provided, skip the check for this field
        // or handle as an error if the field is required.
        // For now, we assume required fields are always present or handled by Sequelize's allowNull.
        return;
      }
      whereClause[field] = value;
    }

    // Add scope fields to the where clause
    for (const field of scope) {
      const value = (instance as any)[field];
      if (value === undefined) {
        // If a scope field is not provided, this might indicate an issue
        // or a global uniqueness check if scope is optional.
        // For now, we assume scope fields are always present if defined.
        return;
      }
      whereClause[field] = value;
    }

    // If it's an update operation, exclude the current instance from the check
    if (!hookOptions.isNewRecord && (instance as any).id) {
      whereClause.id = { [Op.ne]: (instance as any).id };
    }

    // Perform the uniqueness check
    const existingInstance = await (model as any).findOne({
      where: whereClause,
      paranoid: false, // Crucial: disable paranoid filter for this specific check
      transaction: hookOptions.transaction, // Pass transaction if available
    });

    if (existingInstance) {
      throw new Error(errorMessage);
    }
  };
}
