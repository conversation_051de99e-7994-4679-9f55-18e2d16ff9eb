import {
  DataTypes,
  Model,
  Sequelize,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>s<PERSON><PERSON>,
  HasManyGetAssociationsMixin,
  HasManyAddAssociationMixin,
  HasManyCreateAssociationMixin,
  BelongsToGetAssociationMixin,
  Optional,
} from "sequelize";
import { FlowModel } from "./flow.model";
import { TrainingJobModel } from "./training-job.model";
import { FaqCategoryModel } from "./faq-category.model";
import { IntentItemsModel } from "./intent-items.model";
import { BotLanguageModel } from "./bot-language.model";

export enum BotStatus {
  DRAFT = "draft",
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export interface BotAttributes {
  id: string;
  name: string;
  description?: string;
  status: BotStatus;
  domain?: string;
  settings?: Record<string, any>;
  metadata?: Record<string, any>;
  createdBy?: string;
  updatedBy?: string;
  publishedModelId?: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  deletedBy?: string;

  // Associations
  flows?: FlowModel[];
  publishedModel?: TrainingJobModel;
  trainingJobs?: TrainingJobModel[];
}

type BotCreationAttributes = Optional<BotAttributes, "id" | "createdAt" | "updatedAt">;

export class BotModel extends Model<BotAttributes, BotCreationAttributes> implements BotAttributes {
  public id!: string;
  public name!: string;
  public description?: string;
  public status!: BotStatus;
  public settings?: BotSettings;
  public metadata?: Record<string, any>;
  public createdBy?: string;
  public updatedBy?: string;
  public deletedBy?: string;
  public publishedModelId?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
  public readonly deletedAt?: Date;

  // Mixins for associations
  public getFlows!: HasManyGetAssociationsMixin<FlowModel>;
  public addFlow!: HasManyAddAssociationMixin<FlowModel, string>;
  public createFlow!: HasManyCreateAssociationMixin<FlowModel>;

  public getPublishedModel!: BelongsToGetAssociationMixin<TrainingJobModel>;

  // Properties for eager loading
  public flows?: FlowModel[];
  public publishedModel?: TrainingJobModel;
  public trainingJobs?: TrainingJobModel[];
  public faqCategories?: FaqCategoryModel[];
  public intentItems?: IntentItemsModel[];
  public botLanguages?: BotLanguageModel[];

  public static associations: {
    flows: HasMany;
    publishedModel: BelongsTo;
    trainingJobs: HasMany;
    faqCategories: HasMany;
    intentItems: HasMany;
    botLanguages: HasMany;
  };
}

export function initBotModel(sequelize: Sequelize): typeof BotModel {
  BotModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      domain: {
        type: DataTypes.STRING(255),
        allowNull: true,
        defaultValue: "Default",
      },
      status: {
        type: DataTypes.ENUM(BotStatus.DRAFT, BotStatus.ACTIVE, BotStatus.INACTIVE),
        defaultValue: BotStatus.DRAFT,
      },
      settings: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: {},
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      deletedBy: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      publishedModelId: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize,
      tableName: "bots",
      timestamps: true,
      paranoid: true,
      indexes: [
        { fields: ["status"] },
        { fields: ["createdBy"] },
        { fields: ["createdAt"] },
        {
          unique: true,
          fields: ["name", "deletedAt"],
          where: {
            deletedAt: null,
          },
        },
      ],
    },
  );

  return BotModel;
}

export interface BotSettings {
  // NLU Configuration
  nlu: {
    provider: "rasa" | "dialogflow" | "luis" | "custom";
    endpoint?: string;
    apiKey?: string;
    modelName?: string;
    confidenceThreshold: number; // Minimum confidence for intent matching
    fallbackIntent: string; // Intent to use when confidence is too low
  };

  // Session Management
  session: {
    ttlMinutes: number; // Session timeout in minutes
    maxConcurrentSessions: number; // Per user limit
    persistContext: boolean; // Whether to persist context to database
    enableSessionResumption: boolean; // Allow resuming expired sessions
  };

  // Message Handling
  messaging: {
    enableTypingIndicator: boolean;
    typingDelayMs: number; // Delay before showing typing indicator
    maxMessageLength: number; // Maximum message length
    enableQuickReplies: boolean;
    enableRichMessages: boolean; // Cards, carousels, etc.
    defaultErrorMessage: string;
    defaultFallbackMessage: string;
  };

  // Flow Execution
  execution: {
    maxExecutionTimeMs: number; // Maximum time for flow execution
    maxLoopIterations: number; // Prevent infinite loops
    enableAsyncOperations: boolean;
    asyncTimeoutMs: number; // Timeout for async operations
    enableScriptExecution: boolean;
    scriptTimeoutMs: number; // Timeout for script nodes
  };

  // Integration Settings
  integrations: {
    webhook?: {
      url: string;
      secret?: string;
      events: string[]; // Which events to send
    };
    analytics?: {
      enabled: boolean;
      provider?: "google" | "mixpanel" | "custom";
      trackingId?: string;
    };
    logging?: {
      level: "debug" | "info" | "warn" | "error";
      enableChatHistory: boolean;
      retentionDays: number;
    };
  };

  // Security Settings
  security: {
    enableRateLimit: boolean;
    rateLimitPerMinute: number;
    enableInputValidation: boolean;
    allowedFileTypes: string[];
    maxFileSize: number; // In bytes
    enableContentFilter: boolean;
  };
}
