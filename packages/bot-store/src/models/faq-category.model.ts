import type { FaqItemsModel } from "./faq-items.model";
import type { IntentItemsModel } from "./intent-items.model";
import type { EntitiesModel } from "./entities.model";

import {
  DataTypes,
  Model,
  Optional,
  Sequelize,
  BelongsTo,
  BelongsToGetAssociationMixin,
} from "sequelize";
import { BotModel } from "./bot.model";

export enum FaqCategoryType {
  CUSTOM = "CUSTOM",
  DEFAULT = "DEFAULT",
}

export interface FaqCategoryAttributes {
  id: string;
  botId: string;
  name: string;
  type: FaqCategoryType;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  createdBy: string;
  updatedBy: string;
  deletedBy?: string;

  // Associations
  bot?: BotModel;
}

type FaqCategoryCreationAttributes = Optional<
  FaqCategoryAttributes,
  "id" | "createdAt" | "updatedAt" | "deletedAt" | "deletedBy"
>;

export class FaqCategoryModel
  extends Model<FaqCategoryAttributes, FaqCategoryCreationAttributes>
  implements FaqCategoryAttributes
{
  public id!: string;
  public botId!: string;
  public name!: string;
  public type!: FaqCategoryType;
  public createdAt!: Date;
  public updatedAt!: Date;
  public deletedAt?: Date;
  public createdBy!: string;
  public updatedBy!: string;
  public deletedBy?: string;
  public faqItems?: FaqItemsModel[];
  public intentItems?: IntentItemsModel[];
  public entities?: EntitiesModel[];

  // Mixins for associations
  public getBot!: BelongsToGetAssociationMixin<BotModel>;

  // Properties for eager loading
  public bot?: BotModel;

  public static associations: {
    bot: BelongsTo;
  };
}

export function initFaqCategoryModel(sequelize: Sequelize): typeof FaqCategoryModel {
  FaqCategoryModel.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      botId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: "bots",
          key: "id",
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE",
      },
      name: {
        type: DataTypes.STRING(32),
        allowNull: false,
      },
      type: {
        type: DataTypes.ENUM(...Object.values(FaqCategoryType)),
        allowNull: false,
        defaultValue: "CUSTOM",
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      deletedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      sequelize,
      modelName: "FaqCategoryModel",
      tableName: "faq_categories",
      paranoid: true,
      timestamps: true,
      indexes: [
        { fields: ["botId"] },
        {
          unique: true,
          fields: ["name", "botId", "deletedAt"],
          where: {
            deletedAt: null,
          },
        },
      ],
    },
  );

  return FaqCategoryModel;
}
