import { Models } from "../models";

export function parseIncludeQuery(includeQuery: string | undefined, models: Models) {
  if (!includeQuery) return [];

  return includeQuery
    .split(",")
    .map((assoc: string) => {
      switch (assoc.trim()) {
        case "faqTranslations":
          return { model: models.FaqTranslation, as: "faqTranslations" };
        case "category":
          return { model: models.FaqCategory, as: "category" };
        case "intentUtterances":
          return {
            model: models.IntentUtterance,
            as: "intentUtterances",
            include: [{ model: models.UtteranceTranslation, as: "utteranceTranslations" }],
          };
        case "utteranceTranslations":
          return { model: models.UtteranceTranslation, as: "utteranceTranslations" };
        case "entities":
          return { model: models.Entities, as: "entities" };
        case "intentItem":
          return { model: models.IntentItems, as: "intentItem" };
        case "language":
          return { model: models.Language, as: "language" };
        case "bot":
          return { model: models.Bot, as: "bot" };
        case "utterance":
          return {
            model: models.IntentUtterance,
            as: "utterance",
            include: [{ model: models.IntentItems, as: "intentItem" }],
          };
        default:
          return null;
      }
    })
    .filter((assoc) => assoc !== null);
}
