import { QueryInterface } from "sequelize";
import { v4 as uuidv4 } from "uuid";

module.exports = {
  up: async (queryInterface: QueryInterface) => {
    await queryInterface.bulkInsert("languages", [
      { id: uuidv4(), name: "English", code: "en", nativeName: "English", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Mandarin Chinese",
        code: "zh",
        nativeName: "中文 (Zhōngwén)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Hindi",
        code: "hi",
        nativeName: "हिन्दी (Hindī)",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Spanish", code: "es", nativeName: "Español", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Modern Standard Arabic",
        code: "ar",
        nativeName: "العربية الفصحى",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "French", code: "fr", nativeName: "Français", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Bengali",
        code: "bn",
        nativeName: "বাংলা (Bangla)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Portuguese",
        code: "pt",
        nativeName: "Português",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Russian",
        code: "ru",
        nativeName: "Русский (Russkiy)",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Urdu", code: "ur", nativeName: "اردو (Urdū)", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Indonesian",
        code: "id",
        nativeName: "Bahasa Indonesia",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Standard German",
        code: "de",
        nativeName: "Deutsch",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Japanese",
        code: "ja",
        nativeName: "日本語 (Nihongo)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Nigerian Pidgin",
        code: "pcm",
        nativeName: "Pidgin",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Egyptian Arabic",
        code: "arz",
        nativeName: "المصرية‎ (al‑Maṣrī)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Marathi",
        code: "mr",
        nativeName: "मराठी (Marāṭhī)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Vietnamese",
        code: "vi",
        nativeName: "Tiếng Việt",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Telugu",
        code: "te",
        nativeName: "తెలుగు (Telugu)",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Hausa", code: "ha", nativeName: "Hausa", createdAt: new Date() },
      { id: uuidv4(), name: "Turkish", code: "tr", nativeName: "Türkçe", createdAt: new Date() },
      { id: uuidv4(), name: "Swahili", code: "sw", nativeName: "Kiswahili", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Western Punjabi",
        code: "pnb",
        nativeName: "پنجابی / ਪੰਜਾਬੀ (Panjābī)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Tagalog",
        code: "tl",
        nativeName: "Tagalog / Filipino",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Tamil",
        code: "ta",
        nativeName: "தமிழ் (Tamiḻ)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Yue Chinese (Cantonese)",
        code: "yue",
        nativeName: "粵語 (Yuht yúh)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Wu Chinese",
        code: "wuu",
        nativeName: "吳語 (Wúyǔ)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Iranian Persian",
        code: "fa",
        nativeName: "فارسی (Fârsi)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Korean",
        code: "ko",
        nativeName: "한국어 (Hangugeo)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Thai",
        code: "th",
        nativeName: "ไทย (Phasa Thai)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Javanese",
        code: "jv",
        nativeName: "Basa Jawa",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Italian", code: "it", nativeName: "Italiano", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Levantine Arabic",
        code: "apc",
        nativeName: "اللهجة الشامية‎ (ash‑Shāmī)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Amharic",
        code: "am",
        nativeName: "አማርኛ (Amharic)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Kannada",
        code: "kn",
        nativeName: "ಕನ್ನಡ (Kannada)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Bhojpuri",
        code: "bho",
        nativeName: "भोजपुरी (Bhojpurī)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Sudanese Arabic",
        code: "apd",
        nativeName: "السودانية‎ (As‑sūdāniyah)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Southern Min (Min Nan)",
        code: "nan",
        nativeName: "閩南語 (Bân‑lâm‑gú)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Hakka Chinese",
        code: "hak",
        nativeName: "客家語 (Hak‑kâ‑ngî)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Jinyu Chinese",
        code: "cjy",
        nativeName: "晉語 (Jìnyǔ)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Burmese",
        code: "my",
        nativeName: "မြန်မာဘာသာ (Myanmar Bhasa)",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Polish", code: "pl", nativeName: "Polski", createdAt: new Date() },
      { id: uuidv4(), name: "Yoruba", code: "yo", nativeName: "Yorùbá", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Odia (Oriya)",
        code: "or",
        nativeName: "ଓଡିଆ (Oḍiā)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Malayalam",
        code: "ml",
        nativeName: "മലയാളം (Malayāḷaṁ)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Xiang Chinese",
        code: "hsn",
        nativeName: "湘语 (Xiāngyǔ)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Maithili",
        code: "mai",
        nativeName: "मैथिली (Maithilī)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Ukrainian",
        code: "uk",
        nativeName: "Українська (Ukrayins’ka)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Moroccan Arabic",
        code: "ary",
        nativeName: "الدارجة‎ (Darija)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Sundanese",
        code: "su",
        nativeName: "Basa Sunda",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Algerian Arabic",
        code: "arq",
        nativeName: "الدزايرية‎ (Dāzairiya)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Sa’idi Arabic",
        code: "apc2",
        nativeName: "الصعيدي‎ ( aṣ‑Ṣaʿīdī )",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Zulu", code: "zu", nativeName: "isiZulu", createdAt: new Date() },
      { id: uuidv4(), name: "Igbo", code: "ig", nativeName: "Igbo", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Northern Uzbek",
        code: "uz",
        nativeName: "Oʻzbekcha",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Sindhi",
        code: "sd",
        nativeName: "سنڌي (Sindhī)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Nepali",
        code: "ne",
        nativeName: "नेपाली (Nepālī)",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Romanian", code: "ro", nativeName: "Română", createdAt: new Date() },
      { id: uuidv4(), name: "Dutch", code: "nl", nativeName: "Nederlands", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Gan Chinese",
        code: "gan",
        nativeName: "贛語 (Gànyǔ)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Pashto",
        code: "ps",
        nativeName: "پښتو (Pashto)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Magahi",
        code: "mag",
        nativeName: "मगही (Magahī)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Saraiki",
        code: "skr",
        nativeName: "سرائیکی‎ (Saraiki)",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Xhosa", code: "xh", nativeName: "isiXhosa", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Malay",
        code: "ms",
        nativeName: "Bahasa Melayu",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Khmer",
        code: "km",
        nativeName: "ភាសាខ្មែរ (Khmer)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Afrikaans",
        code: "af",
        nativeName: "Afrikaans",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Sinhala",
        code: "si",
        nativeName: "සිංහල (Siṃhala)",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Somali", code: "so", nativeName: "Soomaali", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Chhattisgarhi",
        code: "hne",
        nativeName: "छत्तीसगढ़ी (Chhattīsgarhī)",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Cebuano", code: "ceb", nativeName: "Cebuano", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Mesopotamian Arabic",
        code: "acm",
        nativeName: "العربية العراقية‎ (Al‑ʿArabīyah al‑ʿIrāqīyah)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Assamese",
        code: "as",
        nativeName: "অসমীয়া (Ôxômiya)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Northeastern Thai",
        code: "kdt",
        nativeName: "ภาษาอีสาน (Phasa Isan)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Northern Kurdish",
        code: "kmr",
        nativeName: "Kurdî (Kurmancî)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Hijazi Arabic",
        code: "acw",
        nativeName: "اللهجة الحجازية‎ (al‑Ḥijāzīyah)",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Fulfulde", code: "ff", nativeName: "Fulfulde", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Bavarian",
        code: "bar",
        nativeName: "Bairisch",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Bamanankan",
        code: "bm",
        nativeName: "Bamanankan",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "South Azerbaijani",
        code: "azb",
        nativeName: "گؤنئی آذربایجان داخی آذربایجان دیلی (Güney Azərbaycan dili)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Northern Sotho",
        code: "nso",
        nativeName: "Sepedi",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Setswana", code: "tn", nativeName: "SeTswana", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Southern Sotho",
        code: "st",
        nativeName: "Sesotho",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Czech", code: "cs", nativeName: "Čeština", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Greek",
        code: "el",
        nativeName: "Ελληνικά (Ellīniká)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Chittagonian",
        code: "ctg",
        nativeName: "চাটগাঁইয়া (Chāṭgāĩẏā)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Kazakh",
        code: "kk",
        nativeName: "Қазақша (Qazaqşa)",
        createdAt: new Date(),
      },
      { id: uuidv4(), name: "Swedish", code: "sv", nativeName: "Svenska", createdAt: new Date() },
      { id: uuidv4(), name: "Deccan", code: "dcc", nativeName: "دکن", createdAt: new Date() },
      { id: uuidv4(), name: "Hungarian", code: "hu", nativeName: "Magyar", createdAt: new Date() },
      { id: uuidv4(), name: "Jula", code: "dyu", nativeName: "Jula", createdAt: new Date() },
      {
        id: uuidv4(),
        name: "Sadri",
        code: "sck",
        nativeName: "सदरी (Sadri)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Kinyarwanda",
        code: "rw",
        nativeName: "Kinyarwanda",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Cameroonian Pidgin",
        code: "pms",
        nativeName: "Cameroonian Pidgin",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Sylheti",
        code: "syl",
        nativeName: "সিলেটি (Sileṭī)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "South Levantine Arabic",
        code: "ajp",
        nativeName: "اللهجة الشامية الجنوبية‎ (ash‑Shāmī ash‑Janūbī)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Tunisian Arabic",
        code: "aeb",
        nativeName: "التونسية‎ (at‑Tūnisīyah)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Sanaani Arabic",
        code: "ayn",
        nativeName: "اللهجة الصنعانية‎ (aṣ‑Ṣanʿānīyah)",
        createdAt: new Date(),
      },
      {
        id: uuidv4(),
        name: "Oromo",
        code: "om",
        nativeName: "Afaan Oromoo",
        createdAt: new Date(),
      },
    ]);
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.bulkDelete("languages", {}, {});
  },
};
