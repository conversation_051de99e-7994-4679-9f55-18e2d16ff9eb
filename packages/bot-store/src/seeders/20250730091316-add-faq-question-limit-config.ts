import { QueryInterface } from "sequelize";
import { PlatformConfigKey } from "../models";
import { v4 as uuidv4 } from "uuid";

module.exports = {
  up: async (queryInterface: QueryInterface) => {
    await queryInterface.bulkInsert(
      "platform_configs",
      [
        {
          id: uuidv4(),
          key: PlatformConfigKey.FAQ_QUESTION_LIMIT,
          value: "10",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      {},
    );
  },

  down: async (queryInterface: QueryInterface) => {
    await queryInterface.bulkDelete(
      "platform_configs",
      { key: PlatformConfigKey.FAQ_QUESTION_LIMIT },
      {},
    );
  },
};
