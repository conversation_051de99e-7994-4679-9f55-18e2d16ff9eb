// Include other modules as needed.
const Enums = require("./enums");
const ENVIRONMENT = Enums.ENVIRONMENT;

const UserPrefernceStore = require("preference_store");
const AppCache = require("../cache/AppCache");
const Session = require("../states/Session");
const _ = require("lodash");
const UserSessionStore = require("../cache/UserSessionStore");
var CVTS = null;


module.exports = {
  getExistingOrCreateNew: async function (appId, userId, qs) {
    qs.debuggerService?.log('info', `SessionManager: Attempting to get or create new session for flowId: ${appId}, conversationId: ${userId}`);
    let logprefix = "Session(" + appId + ":" + userId + ")";
    if (global.logger.isTraceEnabled()) {
      global.logger.trace(logprefix + " Query string params " + JSON.stringify(qs));
      global.logger.trace(logprefix + " User Input " + qs.subscriberInput);
    }

    let session = await UserSessionStore.get(appId, userId);
    if (typeof session == "string") {
      session = JSON.parse(session);
      session = new Session(session);
    }


    if (session != null) {
      qs.debuggerService?.log('info', `SessionManager: Existing session found for flowId: ${appId}, conversationId: ${userId}`);
      if (global.logger.isTraceEnabled()) { global.logger.trace(logprefix + " Existing Session"); }
      if (qs.clean == global.config.app_engine.sessionCleanKey) {
        qs.subscriberInput = qs.clean;
      }
      if (qs.subscriberInput != null) {
        session.getContext().setSubsInput(String(qs.subscriberInput));
      }
      let additionalQueryParams = qs;

      Object.keys(additionalQueryParams).forEach((key) => {
        if (CVTS != null && CVTS.includes(key) != -1) {
          session.setParam(key, JSON.stringify(additionalQueryParams[key]));
        } else if(key === 'journeyContext'){
          session.journeyContext = {
            ...session.journeyContext,
            ...additionalQueryParams[key]
          }
        } 
        else {
          session.setParam(key, additionalQueryParams[key]);
          if (key == "SIMSERIAL") {
            session.setParam(key, JSON.stringify(additionalQueryParams[key]));
          }
        }
      });
      session.setIsNewRequest(false);
      return session;
    } else {
      qs.debuggerService?.log('info', `SessionManager: Creating new session for flowId: ${appId}, conversationId: ${userId}`);
      if (global.logger.isTraceEnabled()) { global.logger.trace(logprefix + " New Session"); }

      if (qs.debugId != null) {
        qs.debuggerService?.log('info', `SessionManager: Running in debug mode for flowId: ${appId}`);
        if (global.logger.isTraceEnabled()) { global.logger.trace(logprefix + " Running in debug mode"); }
      }
      qs.isNewRequest = 1;
      let startId = AppCache.getStartModuleID(appId, qs.appType);
      let endId = AppCache.getEndModuleID(appId, qs.appType);
      const moduleId = startId;
      const moduleData = AppCache.getModuleData(appId, moduleId, qs.appType);
      const mode = qs.debugId && ENVIRONMENT.DEV || ENVIRONMENT.PROD;
      const contextData = createNewContext(moduleId, moduleData, qs);
      if (global.config.app_engine.userPreferenceStoreCheck) {
        if (qs.language == null) {
          qs.language = await UserPrefernceStore.get(userId);
        } else {
          await UserPrefernceStore.set(userId, qs.language);
        }
      } else if (qs.language == null) {
        qs.language = global.config.defaultLanguage;
      }
      if (qs.contentType == null) {
        qs.contentType = "text";
      }
      let objectt = Object.assign({}, contextData, {
        settings: Object.assign({}, contextData.settings, { password: "xxxxxxxx" }) // NOSONAR : This is just a sample value
      });
      if (global.logger.isTraceEnabled()) {
        global.logger.trace(logprefix + "|startId: " + startId + "|endId: " + endId + "|moduleId:" + moduleId + "|plugin:" + moduleData.type + "|mode: " + mode + "|context: " + JSON.stringify(objectt));
      }
      let session = new Session({
        id: String(qs.txnId),
        originAppId: appId,
        appId: appId,
        from: qs.from,
        userId: userId,
        moduleId: moduleId,
        type: moduleData.type,
        context: contextData,
        mode: mode,
        imode: qs.imode,
        locale: qs.language,
        invoker: qs.debugId,
        endModuleId: endId,
        freeflow: moduleData.settings.freeflow,
        dataTable: {},
        journeyContext: qs.journeyContext,
        debuggerService: qs.debuggerService,
        appType: qs.appType
      });

      let locale = session.getLocale() || global.config.defaultLanguage;
      if (locale != global.config.defaultLanguage && session.getContext().getMenuContext()) {
        await session.getContext().getMenuContext().loadTemplate();
      }
      if (qs.subscriberInput != null) {
        session.getContext().setSubsInput(String(qs.subscriberInput));
      }
      session.setIsNewRequest(true);
      qs.debuggerService?.log('info', `SessionManager: New session context prepared for flowId: ${appId}, moduleId: ${moduleId}`);
      return session;
    }
  },
  getExpiredSession: async function (appId, userId, appType = 'Live') {
    let session = await UserSessionStore.get("meta_" + appId, userId, appType);
    if (typeof session == "string") {
      session = JSON.parse(session);
      session = new Session(session);
    }

    if (session != null) {
      session.setParam('code', '899');
      return session;
    } else {
      global.logger.error("Session not found for:" + appId + "+" + userId);
    }
  }
};

function createNewContext(moduleId, moduleData, query) {
  let opts = {};
  if (moduleData !== null) {
    opts = {
      mid: moduleId,
      query: query,
      typeId: moduleData.typeId,
      mname: moduleData.type,
      input: moduleData.input,
      settings: moduleData.settings,
      process: _.merge(moduleData.process, query),
      output: moduleData.output,
      conditionsMap: moduleData.conditionsMap,
      menuNavigationOptions: moduleData.menuNavigationOptions
    };
  }
  return opts;
}
