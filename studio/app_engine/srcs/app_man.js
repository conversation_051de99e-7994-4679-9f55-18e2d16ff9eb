"use strict";
/**
 *  Application Manager -- A module that is responsible for
 *  -- loading, launching LEAP applications
 *  -- providing state management and persistence support
 *
 **/
// Include other modules as needed.
const Enums = require("./lib/enums");
const INTERFACES = Enums.INTERFACES;

const AppCache = require("./cache/AppCache");

const ResponseHandler = require("./lib/ResponseHandler");
const NexmoMapping = require("../config/nexmo.json").keywordMapping;
const utility = require("utility");
const SessionManager = require("./lib/SessionManager");
const KPI = require("app_kpi");
const KPI_INT_REQ = KPI.KEYS.interface_req;
const VALCODES = require("./lib/http_codes");

module.exports = {

  /**
   * Flow Engine Interface - New interface for conversation modules
   */
   flowEngineInterface: async (
    appId,
    userId,
    queryParams,
    flowEngineCallback
  ) => {
    try {
      KPI.emit(KPI_INT_REQ, INTERFACES.FLOW_ENGINE);
  
      if (!AppCache.exists(appId, queryParams.appType)) { 
          queryParams.debuggerService?.log('error', `Flow Engine Interface: Flow ${appId} not found.`);
          const errorResponse = {
            success: false,
            error: "Application not found",
            code: VALCODES.APP_NOT_FOUND
          };
          return flowEngineCallback(errorResponse);
      }
      
      const qs = {
        ...queryParams,
        imode: INTERFACES.FLOW_ENGINE,
        txnId: utility.getUniqueTxnId(),
      };
  
      const session = await SessionManager.getExistingOrCreateNew(
        appId,
        userId,
        qs
      );
  
      session.setDebuggerService(queryParams.debuggerService);
      session.setFlowEngineCallback(flowEngineCallback);
      queryParams.debuggerService?.log('info', `Flow Engine Interface: Starting session for flowId: ${appId}, conversationId: ${userId}`);
      return session.start();

    } catch (error) {
      queryParams.debuggerService?.log('error', `Flow Engine Interface: Failed to process request for flowId: ${appId}, conversationId: ${userId}. Error: ${error.message}`);
      global.logger.error("Failed to process Flow Engine request", error);
      const errorResponse = {
        success: false,
        error: error.message,
        code: VALCODES.INTERNAL_ERROR
      };
      flowEngineCallback(errorResponse);
    }
  },
};