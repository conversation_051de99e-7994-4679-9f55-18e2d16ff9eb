"use strict";
/**
 *  Interactive Message Node Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class InteractiveMessagePlugin
 */
class InteractiveMessagePlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  _buildCardMessage(cardData, journeyContext) {
    const title = this.getCompiledTemplate(cardData?.title, journeyContext);
    const description = this.getCompiledTemplate(cardData?.description, journeyContext);
    const buttons =
      cardData?.buttons?.map((btn) => ({
        id: btn.id,
        title: this.getCompiledTemplate(btn.title, journeyContext),
        payload: btn.payload,
      })) || [];

    return {
      type: "card",
      imageUrl: cardData?.imageUrl,
      title,
      description,
      buttons,
      timestamp: new Date().toISOString(),
    };
  }
  exec(context) {
    return new Promise(async (resolve) => {
      try {
        const { journeyContext, process } = context;
        const { channelData } = process;
        const { channelType = "web", language = "english" } = journeyContext || {};
        // Get language-specific content from channelData
        const languageContent =
          channelData?.[channelType]?.[language] || channelData?.[channelType]?.english;

        // Build interactive message
        const interactiveMessage = this._buildCardMessage(
          languageContent?.cardContent,
          journeyContext,
        );

        // Store in global context
        this.storeSessionData(context, `interactive_${context.mid}`, interactiveMessage);

        resolve({
          code: error_codes.success,
          messageResponse: interactiveMessage,
          nodeType: "interactiveMessage",
        });
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }

  close() {}
}

module.exports = InteractiveMessagePlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./interactiveMessageSchema.json");
  schema.category = "interactiveMessage";
  return schema;
}
