{"typeId": "2.6", "name": "InteractiveMessage", "title": "Interactive Message", "description": "Send interactive messages with buttons and multi-language support", "type": "object", "required": ["name", "coordinates", "process", "output"], "properties": {"name": {"description": "Name of the module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates of the module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "process": {"description": "Processing properties of the module", "title": "Process", "type": "object", "required": ["channelData"], "properties": {"channelData": {"description": "Channel-specific and multi-language content", "title": "Channel Data", "type": "object", "properties": {"web": {"type": "object", "properties": {"english": {"$ref": "#/definitions/languageContent"}, "hindi": {"$ref": "#/definitions/languageContent"}, "german": {"$ref": "#/definitions/languageContent"}, "arabic": {"$ref": "#/definitions/languageContent"}}, "required": ["english"], "additionalProperties": false}, "whatsapp": {"type": "object", "properties": {"english": {"$ref": "#/definitions/languageContent"}, "hindi": {"$ref": "#/definitions/languageContent"}, "german": {"$ref": "#/definitions/languageContent"}, "arabic": {"$ref": "#/definitions/languageContent"}}, "required": ["english"], "additionalProperties": false}}, "required": ["web"], "additionalProperties": false}, "storeGlobally": {"description": "Store message in global session context", "title": "Store Globally", "type": "boolean", "default": false}}}, "output": {"description": "The output params", "type": "object"}}, "definitions": {"languageContent": {"type": "object", "properties": {"cardContent": {"type": "object", "properties": {"imageUrl": {"type": "string", "format": "uri"}, "title": {"type": "string"}, "description": {"type": "string"}, "buttons": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "payload": {"type": "string"}}}}, "required": ["title"], "additionalProperties": false}}, "required": ["cardContent"]}, "additionalProperties": false}}}