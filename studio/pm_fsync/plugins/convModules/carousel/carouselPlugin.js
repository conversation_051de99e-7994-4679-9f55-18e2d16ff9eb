"use strict";
/**
 *  Carousel Node Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class CarouselPlugin
 */
class CarouselPlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  _buildCardMessage(cardData, journeyContext) {
    const title = this.getCompiledTemplate(cardData?.title, journeyContext);
    const description = this.getCompiledTemplate(cardData?.description, journeyContext);
    const buttons =
      cardData?.buttons?.map((btn) => ({
        id: btn.id,
        title: this.getCompiledTemplate(btn.title, journeyContext),
        payload: btn.payload,
      })) || [];

    return {
      type: "card",
      imageUrl: cardData?.imageUrl,
      title,
      description,
      buttons,
      timestamp: new Date().toISOString(),
    };
  }

  exec(context) {
    return new Promise(async (resolve) => {
      try {
        const { journeyContext, process } = context;
        const { channelData } = process;
        const { channelType = "web", language = "english" } = journeyContext || {};

        const languageContent =
          channelData?.[channelType]?.[language] || channelData?.[channelType]?.english;

        // Build Carousel
        const carouselData =
          languageContent?.carouselContent?.map((cardItem) =>
            this._buildCardMessage(cardItem, journeyContext),
          ) || [];

        // Store in global context
        this.storeSessionData(context, `carousel_${context.mid}`, carouselData);

        resolve({
          code: error_codes.success,
          messageResponse: carouselData,
          nodeType: "carousel",
        });
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }

  close() {}
}

module.exports = CarouselPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./carouselSchema.json");
  schema.category = "carousel";
  return schema;
}
