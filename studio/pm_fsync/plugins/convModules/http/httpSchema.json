{"typeId": "1.2", "name": "http", "title": "HTTP Push", "description": "HTTP Channel functions as a request– response protocol in the client– server computing mode", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "required": ["nodeName"], "properties": {"timeout": {"description": "The time interval defines the Request timeout in millis", "title": "Request Timeout in ms", "default": 10000, "type": "integer"}, "nodeName": {"description": "Name of the node", "title": "Node Name", "type": "string"}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"description": "Setting properties Of the Module", "title": "Process", "type": "object", "required": ["method", "url"], "properties": {"method": {"description": "HTTP Request Method", "title": "HTTP Request Method", "enum": ["GET", "POST", "PUT", "DELETE", "PURGE"]}, "url": {"description": "URL of the connecting server", "title": "URL", "type": "string", "fromat": "uri", "minLength": 1}, "headers": {"description": "Request Headers", "title": "Request Header", "hint": "key-value", "type": "object"}, "qs": {"description": "Request Parameters with key-value", "title": "Request Params", "hint": "key-value", "type": "object"}, "form": {"description": "Request payload", "title": "Request Payload"}, "cert": {"description": "Certifcate file for HTTPS request", "title": "Certificate file path", "type": "string"}, "encoding": {"description": "Encoding used in http request", "title": "Encoding", "type": "string", "default": "utf-8"}, "key": {"description": "Key file for HTTPS request", "title": "Key file path", "type": "string"}, "ca": {"description": "Certificate Authorities file for HTTPS request", "title": "Certificate Authorities(CA) file path", "type": "string"}, "rejectUnauthorized": {"description": "Reject Unauthorized", "title": "Reject Unauthorized", "enum": [true, false]}, "passphrase": {"description": "Request Parameters with key-value", "title": "Password for HTTPS request", "type": "string"}}, "oneOf": [{"properties": {"method": {"enum": ["POST", "PUT", "DELETE", "PURGE"]}}, "required": ["form"]}, {"properties": {"method": {"enum": ["GET"]}}, "required": ["qs"]}]}, "output": {"description": "The output params", "type": "object"}}}