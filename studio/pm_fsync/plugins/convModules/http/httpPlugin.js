"use strict";
/**
 *  HTTP Plugin
 *
 *  <AUTHOR>
 **/
const parser = require("xml2json");
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const qs = require("querystring");
const error_codes = message.error_codes;
const CDRWriter = require("../../pluginCDRWriter");
const path = require("path");
const fs = require("fs");
const Iconv = require('iconv').Iconv;

const NS_PER_SEC = 1e9;

let schema;

/**
 * @class HTTPPlugin
 */
class HTTPPlugin extends ConvModulePlugin {

    init() {

    }

    /**
     * method getMetaDataInfo
     * @returns
     * @memberof HTTPPlugin
     */
    getMetaDataInfo() {
        return super.getMetaDataInfo(loadSchemaFile());
    }

    /**
     * method validate
     * @param {any} module
     * @returns
     * @memberof HTTPPlugin
     */
    validate(module) {
        loadSchemaFile();
        return super.validate(module, schema);
    }

    /**
     * method exec
     * @param {any} context
     * @returns
     * @memberof HTTPPlugin
     */

    async exec(context) {
        try {
            let jsonRequest = {
                "URL": context.process.URL,
                "type": context.process.requestType || "GET",
                "timeout": context.settings.timeout || 10000
            }

            if (context.process.headers) {
                jsonRequest.headerBody = setHeaders(context.process.headers);
            }

            if (context.process.encoding == "null") {
                jsonRequest.encoding = null;
            }
            jsonRequest.encoding = "utf-8";

            if (jsonRequest.URL.startsWith("https://")) {
                try {
                    if (context.process.cert != null) {
                        jsonRequest.certificatePath = fs.readFileSync(path.resolve(context.process.cert));
                    }
                    if (context.process.key != null) {
                        jsonRequest.keyPath = fs.readFileSync(path.resolve(context.process.key));
                    }
                    if (context.process.ca != null) {
                        jsonRequest.caPath = fs.readFileSync(path.resolve(context.process.ca));
                    }
                    if (context.process.passphrase != null) {
                        jsonRequest.passphrase = context.process.passphrase;
                    }
                    jsonRequest.rejectUnauthorized = context.process.rejectUnauthorized || false;
                    jsonRequest.securityOptions = "TLSv1_2_method";
                } catch (h) {
                    //ignore
                }
            }
            if (jsonRequest.requestType != "GET") {
                let contentType = jsonRequest.headerBody["Content-Type"] || "application/json";
                if (contentType.includes("json")) {
                    if (typeof context.process.requestBody == "object") {
                        jsonRequest.requestBody = context.process.requestBody;
                    }
                    else {
                        jsonRequest.requestBody = JSON.parse(context.process.requestBody || "{}");
                    }
                } else if (contentType.includes("xml")) {
                    jsonRequest.requestBody = context.process.requestBody;
                } else {
                    jsonRequest.requestBody = context.process.requestBody;
                }
            }

            const startTime = Date.now();

            try {
                const response = await super.httpCall(jsonRequest);
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, responseTime, response.code, context.process, response.data, context.query.txnId, context.userId);
                this.storeSessionData(context.journeyContext, `httpResponse_${context.mid}`, response);
                return {
                    code: response.code,
                    msg: response.data
                };
            } catch (error) {
                this.storeSessionData(context.journeyContext, `httpResponse_${context.mid}`, error);
            }
        } catch (error) {
            global.logger.error(error);
            CDRWriter.emit("EXEC_CDR", context.appId, context.mid, context.pluginName, '-', error_codes.pluginInternalError, '-', context.query.txnId, context.userId);
            return {
                code: error_codes.pluginInternalError,
                msg: error
            };
        } finally {

        }
    }

    close() {

    }
}
module.exports = HTTPPlugin;

/* loads the app start schema file (once and only once) and returns its schema */
function loadSchemaFile() {
    if (schema) {
        return schema;
    }
    schema = require("./httpSchema.json");
    schema.category = "http";
    return schema;
}

function setHeaders(headers) {
    let keys = Object.keys(headers);
    let res = {};
    try {
        for (let i = 0; i < keys.length; i++) {
            let obj = headers[keys[i]];
            if (obj.headerKey.trim().length > 0) {
                res[obj.headerKey] = obj.headerValue;
            }
        }
    } catch (error) {

    }
    return res;
}
