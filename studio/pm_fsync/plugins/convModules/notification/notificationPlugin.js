"use strict";
/**
 *  Notification Node Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const SMSPlugin = require("../../channelModules/sms/smsPlugin");
const EmailPlugin = require("../../channelModules/email/emailPlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class NotificationPlugin
 */
class NotificationPlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  exec(context) {
    return new Promise(async (resolve) => {
      try {
        const { channels } = context.settings;
        const { sms: smsSettings, email: emailSettings } = context.settings;
        const { channelData } = context.process;
        const { channelType = "web", language = "english" } = context.journeyContext || {};

        const notification = {
          timestamp: new Date().toISOString(),
          channels: channels,
        };

        const smsPlugin = new SMSPlugin();
        const emailPlugin = new EmailPlugin();

        let smsResult = null;
        let emailResult = null;

        const langContent =
          channelData?.[channelType]?.[language] || channelData?.[channelType]?.english;

        if (channels.includes("sms") || channels.includes("both")) {
          const smsMessageBody = langContent?.smsMessageBody;

          if (
            !smsSettings ||
            !smsSettings.senderId ||
            !smsSettings.recipientMsisdn ||
            !smsMessageBody
          ) {
            return resolve({
              code: error_codes.validationError,
              msg: "SMS configuration is incomplete or message body is missing.",
            });
          }
          if (!/^\+?[1-9]\d{1,14}$/.test(smsSettings.recipientMsisdn)) {
            return resolve({
              code: error_codes.validationError,
              msg: "Invalid SMS recipient MSISDN format.",
            });
          }
          if (smsMessageBody.length > 320) {
            return resolve({
              code: error_codes.validationError,
              msg: "SMS message body exceeds 320 characters.",
            });
          }

          const smsContext = {
            appId: context.appId,
            mid: context.mid,
            pluginName: "SMS",
            query: context.query,
            settings: {
              authorization: context.settings.authorization,
            },
            process: {
              text_message: smsMessageBody,
              receiverAddress: smsSettings.recipientMsisdn,
              senderAddress: smsSettings.senderId,
            },
          };
          smsResult = await smsPlugin.exec(smsContext);
          notification.sms = smsResult;
        }

        if (channels.includes("email") || channels.includes("both")) {
          const emailSubject = langContent?.emailSubject;
          const emailMessageBody = langContent?.emailMessageBody;

          if (
            !emailSettings ||
            !emailSettings.senderEmail ||
            !emailSettings.recipientEmail ||
            !emailSubject ||
            !emailMessageBody
          ) {
            return resolve({
              code: error_codes.validationError,
              msg: "Email configuration is incomplete or message content is missing.",
            });
          }
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailSettings.recipientEmail)) {
            return resolve({
              code: error_codes.validationError,
              msg: "Invalid Email recipient address format.",
            });
          }
          if (emailMessageBody.length > 320) {
            return resolve({
              code: error_codes.validationError,
              msg: "Email message body exceeds 320 characters.",
            });
          }

          const emailContext = {
            appId: context.appId,
            mid: context.mid,
            pluginName: "email",
            query: context.query,
            settings: {
              authorization: context.settings.authorization,
            },
            process: {
              body: emailMessageBody,
              senderId: emailSettings.senderEmail,
              receiverAddress: emailSettings.recipientEmail,
              subject: emailSubject,
            },
          };
          emailResult = await emailPlugin.exec(emailContext);
          notification.email = emailResult;
        }

        const globalCtx = this.getGlobalContext(context);
        globalCtx.sessionData[`notification_${context.mid}`] = notification;

        resolve({
          code: error_codes.success,
          notification,
          nodeType: "notification",
        });
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }

  close() {}
}

module.exports = NotificationPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./notificationSchema.json");
  schema.category = "notification";
  return schema;
}
