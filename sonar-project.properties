sonar.branch.name=ng-mainline-dev
sonar.host.url=http://sonar.comviva.com/sonar
sonar.links.ci=http://************:8080/job/MCS_CPAAS_8x_RM_ng-neuratalk
sonar.links.scm=http://blrgitlab.comviva.com/mbs/ng/sf/chatbot/ng-neuratalk.git
sonar.projectVersion=8.0
sonar.sources=bot-builder-service/src,bot-interaction-service/src,studio/pm_fsync,rasa-server/src,training-service/src
sonar.nodejs.executable=/usr/bin/node
sonar.cfamily.build-wrapper-output.bypass=true
sonar.projectName=MCS_CPAAS_8x_RM_ng-neuratalk
sonar.projectKey=my:MCS_CPAAS_8x_RM_ng-neuratalk
sonar.exclusions=**/test/**,**/packages/**,**/eslint-local-rules/**,**/chat-service/**,**/chat-gateway/**,**/studio/api_gw/**,**/studio/app_engine/**,**/studio/app_kpi/**,**/studio/app_macros/**,**/studio/app_session_cleaner/**,**/studio/app_store/**,**/studio/,app_validation/**,**/studio/authorizer/**,**/studio/cdr_processor/**,**/studio/common/**,**/studio/config-tree/**,**/studio/docker/**,**/studio/extlibs/**,**/studio/,message/**,**/studio/msisdn-validator/**,**/studio/oam/**,**/studio/PluginManager/**,**/studio/preference_store/**,**/studio/session_extender/**,**/studio/,studio_ai/**,**/studio/tps_agent/**,**/studio/utility/**