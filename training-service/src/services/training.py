# src/services/training.py
import logging
import shutil
from pathlib import Path
from rasa.api import train

from src.config import settings
from src.database.manager import DatabaseManager
from src.services.rasa_generator import RasaFileGenerator
from src.services.storage import StorageService
from src.services.kafka_producer import TrainingResultProducer

logger = logging.getLogger(__name__)

class TrainingOrchestrator:
    def __init__(self, db_manager: DatabaseManager, storage_service: StorageService, result_producer: TrainingResultProducer):
        self.db_manager = db_manager
        self.storage_service = storage_service
        self.result_producer = result_producer
        self.training_data_path = settings.training_data_path
        self.models_output_path = settings.models_output_path

    async def process_job(self, bot_id: str, job_id: str):
        logger.info(f"Starting training process for job_id: {job_id}, bot_id: {bot_id}")

        self.result_producer.send_result({"jobId": job_id, "botId": bot_id, "status": "RUNNING"})

        job_path = self.training_data_path / bot_id / job_id
        model_output_path = self.models_output_path / job_id
        
        try:
            # 1. Fetch all required data from the new schema
            logger.info("Fetching data from database...")
            bot_languages = self.db_manager.fetch_bot_languages(bot_id)
            if not bot_languages:
                raise ValueError(f"No languages configured for bot {bot_id}. Aborting training.")

            faq_translations = self.db_manager.fetch_faq_translations(bot_id)
            intent_utterance_translations = self.db_manager.fetch_intent_utterance_translations(bot_id)
            entities = self.db_manager.fetch_entities(bot_id)
            logger.info("Data fetching complete.")

            # 2. Generate Rasa files
            generator = RasaFileGenerator(
                bot_id, 
                job_id, 
                bot_languages,
                faq_translations,
                intent_utterance_translations,
                entities,
                self.training_data_path
            )
            generator.run()

            # 3. Train Rasa model
            logger.info(f"Initiating Rasa training for job {job_id}")
            model_output_path.mkdir(exist_ok=True)
            
            fixed_model_name = f"{bot_id}-{job_id}"
            
            # Rasa automatically detects language subdirectories in the 'data' path
            trained_model_path_str = train(
                domain=str(job_path / 'domain.yml'),
                config=str(job_path / 'config.yml'),
                training_files=str(job_path / 'data'),
                output=str(model_output_path),
                fixed_model_name=fixed_model_name
            )

            # train returns a result object now
            if not trained_model_path_str or not trained_model_path_str.model:
                 raise RuntimeError("Rasa training failed to produce a model file.")
            
            trained_model_path = Path(trained_model_path_str.model)
            logger.info(f"Rasa model trained successfully: {trained_model_path}")

            # 4. Upload model to S3
            s3_object_name = f"models/{bot_id}/{trained_model_path.name}"
            model_s3_url = self.storage_service.upload_file(trained_model_path, s3_object_name)
            logger.info(f"Model uploaded to S3: {model_s3_url}")

            # 5. Update job status to completed
            success_payload = {
                "jobId": job_id,
                "botId": bot_id,
                "status": "COMPLETED",
                "modelUrl": model_s3_url
            }
            self.result_producer.send_result(success_payload)
            logger.info(f"Job {job_id} completed successfully. Result message sent.")


        except Exception as e:
            error_message = f"Training failed for job {job_id}: {e}"
            logger.error(error_message, exc_info=True)
            failure_payload = {
                "jobId": job_id,
                "botId": bot_id,
                "status": "FAILED",
                "errorMessage": str(e)
            }
            self.result_producer.send_result(failure_payload)
        finally:
            # 6. Cleanup local files
            if job_path.exists():
                # shutil.rmtree(job_path)
                logger.info(f"Cleaned up training data directory: {job_path}")
            if model_output_path.exists():
                # shutil.rmtree(model_output_path)
                logger.info(f"Cleaned up model output directory: {model_output_path}")