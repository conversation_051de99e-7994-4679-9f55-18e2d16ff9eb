# src/services/rasa_generator.py

import logging
import yaml
from pathlib import Path
from typing import List
from collections import defaultdict

from src.database.models import (
    Languages, FaqTranslations, IntentUtteranceTranslations, Entities
)

logger = logging.getLogger(__name__)

class LiteralString(str):
    pass

def literal_string_representer(dumper, data):
    return dumper.represent_scalar('tag:yaml.org,2002:str', data, style='|')

yaml.add_representer(LiteralString, literal_string_representer)

class RasaFileGenerator:
    def __init__(
        self,
        bot_id: str,
        job_id: str,
        bot_languages: List[Languages],
        faq_translations: List[FaqTranslations],
        intent_utterance_translations: List[IntentUtteranceTranslations],
        entities: List[Entities],
        base_path: Path
    ):
        self.bot_id = bot_id
        self.job_id = job_id
        self.bot_languages = bot_languages
        self.faq_translations = faq_translations
        self.intent_utterance_translations = intent_utterance_translations
        self.entities = entities
        self.job_path = base_path / bot_id / job_id
        self.data_path = self.job_path / "data"

    def _prepare_directories(self):
        logger.info(f"Creating directories at {self.job_path}")
        self.data_path.mkdir(parents=True, exist_ok=True)
        for lang in self.bot_languages:
            lang_path = self.data_path / lang.code
            lang_path.mkdir(exist_ok=True)
            logger.info(f"Created language directory: {lang_path}")

    def _generate_nlu(self):
        # --- CHANGE: Initialize nlu_data with the mandatory language key ---
        nlu_data_by_lang = defaultdict(lambda: {'version': '3.1', 'nlu': []})

        for trans in self.faq_translations:
            lang_code = trans.language.code
            faq_id = trans.faq_item.id
            flow_id = trans.faq_item.flowId
            intent_name = f"faq_{faq_id}_{lang_code}_{flow_id}"
            
            # --- CHANGE: Set the language key for the specific language file ---
            if 'language' not in nlu_data_by_lang[lang_code]:
                nlu_data_by_lang[lang_code]['language'] = lang_code
            
            if not trans.questions: continue

            bulleted_examples = [f"- {q}" for q in trans.questions]
            nlu_data_by_lang[lang_code]['nlu'].append({
                'intent': intent_name,
                'examples': LiteralString("\n".join(bulleted_examples))
            })

        nlu_data_by_lang_obj = {}
        for trans in self.intent_utterance_translations:
            lang_code = trans.language.code
            intent_id = trans.utterance.intent.id
            flow_id = trans.utterance.intent.flowId
            intent_name = f"intent_{intent_id}_{lang_code}_{flow_id}"

            # --- CHANGE: Set the language key for the specific language file ---
            if 'language' not in nlu_data_by_lang[lang_code]:
                nlu_data_by_lang[lang_code]['language'] = lang_code

            if lang_code not in nlu_data_by_lang_obj:
                nlu_data_by_lang_obj[lang_code] = {}

            if intent_name not in nlu_data_by_lang_obj[lang_code]:
                nlu_data_by_lang_obj[lang_code][intent_name] = []

            nlu_data_by_lang_obj[lang_code][intent_name].append(trans.text)

        for lang_code, nlu_data in nlu_data_by_lang_obj.items():
            for intent_name, examples in nlu_data.items():
                nlu_data_by_lang[lang_code]['nlu'].append({
                    'intent': intent_name,
                    'examples': LiteralString("\n".join([f"- {example}" for example in examples]))
                })
            
        for lang_code, nlu_data in nlu_data_by_lang.items():
            if not nlu_data['nlu']:
                logger.warning(f"No NLU data found for language '{lang_code}'. Skipping nlu.yml file.")
                continue
            
            # Add fallback intent
            nlu_data['nlu'].append({
                'intent': 'nlu_fallback',
                'examples': LiteralString("""- I don't understand
- Can you rephrase that?
- What do you mean?
- I'm not sure what to say
- I don't know""")
            })
            
            nlu_file_path = self.data_path / lang_code / "nlu.yml"
            with open(nlu_file_path, 'w', encoding='utf-8') as f:
                # The 'language' key is now part of the nlu_data dictionary
                yaml.dump(nlu_data, f, allow_unicode=True, sort_keys=False)
            logger.info(f"Generated {nlu_file_path}")

    def _generate_domain(self):
        # No changes needed here. This logic is correct.
        intents = set()
        responses = {}

        for trans in self.faq_translations:
            lang_code = trans.language.code
            faq_id = trans.faq_item.id
            intent_name = f"faq_{lang_code}_{faq_id}"
            intents.add(intent_name)
            response_name = f"utter_{intent_name}"
            responses[response_name] = [{"text": trans.answer}]
        
        for trans in self.intent_utterance_translations:
            lang_code = trans.language.code
            intent_id = trans.utterance.intent.id
            intent_name = f"intent_{lang_code}_{intent_id}"
            intents.add(intent_name)

        entity_names = [f"{e.name}_{e.id}_{e.type}" for e in self.entities]

        domain_data = {
            'version': '3.1',
            'intents': sorted(list(intents)),
            'entities': entity_names,
            'responses': responses,
            'session_config': {'session_expiration_time': 60, 'carry_over_slots_to_new_session': True},
        }

        with open(self.job_path / "domain.yml", 'w', encoding='utf-8') as f:
            yaml.dump(domain_data, f, allow_unicode=True, sort_keys=False)
        logger.info("Generated domain.yml")

    def _generate_rules(self):
        # No changes needed here. This logic is correct.
        rules = {'version': '3.1', 'rules': []}

        for trans in self.faq_translations:
            lang_code = trans.language.code
            faq_id = trans.faq_item.id
            intent_name = f"faq_{lang_code}_{faq_id}"
            action_name = f"utter_{intent_name}"
            
            rules['rules'].append({
                'rule': f"Respond to {intent_name}",
                'steps': [{'intent': intent_name}, {'action': action_name}]
            })
            
        if not rules['rules']:
            logger.warning("No FAQ rules to generate. rules.yml will be empty.")

        with open(self.data_path / "rules.yml", 'w', encoding='utf-8') as f:
            yaml.dump(rules, f, allow_unicode=True, sort_keys=False)
        logger.info("Generated rules.yml")

    def _generate_config(self):
        # --- CHANGE: Remove the 'language' key from config.yml ---
        config_data = {
            'recipe': 'default.v1',
            # 'language': ...,  <-- KEY REMOVED
            'pipeline': [
                {'name': 'WhitespaceTokenizer'},
                {'name': 'RegexFeaturizer'},
                {'name': 'LexicalSyntacticFeaturizer'},
                {'name': 'CountVectorsFeaturizer'},
                {'name': 'CountVectorsFeaturizer', 'analyzer': 'char_wb', 'min_ngram': 1, 'max_ngram': 4},
                {'name': 'DIETClassifier', 'epochs': 100},
                {'name': 'EntitySynonymMapper'},
                {
                    'name':'FallbackClassifier',
                    'threshold': 0.3,
                    'ambiguity_threshold': 0.1
                }
            ],
            'policies': [
                {'name': 'RulePolicy'},
                {'name': 'MemoizationPolicy'},
                {'name': 'TEDPolicy', 'max_history': 5, 'epochs': 100},
            ]
        }
        with open(self.job_path / "config.yml", 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, sort_keys=False, allow_unicode=True)
        logger.info("Generated config.yml without top-level language key.")

    def run(self) -> Path:
        if not self.bot_languages:
            raise ValueError("Cannot generate files: Bot has no configured languages.")
        self._prepare_directories()
        self._generate_nlu()
        self._generate_domain()
        self._generate_rules()
        self._generate_config()
        return self.job_path