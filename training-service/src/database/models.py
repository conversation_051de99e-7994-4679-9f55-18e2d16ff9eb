# src/database/models.py
import uuid
from sqlalchemy import (
    Column, DateTime, String, Text, ForeignKey, JSON, func
)
from sqlalchemy.orm import relationship, declarative_base
from sqlalchemy.dialects.postgresql import UUID

Base = declarative_base()

# Helper function for default UUIDs
def generate_uuid():
    return str(uuid.uuid4())

class Languages(Base):
    __tablename__ = 'languages'
    id = Column(String(36), primary_key=True, default=generate_uuid)
    name = Column(Text, unique=True, nullable=False)
    code = Column(String(10), unique=True, nullable=False)
    createdAt = Column(DateTime, server_default=func.now())
    # createdBy, updatedBy, deletedBy can be added if you have a users table
    
    # Relationships
    bot_languages = relationship("BotLanguages", back_populates="language")
    faq_translations = relationship("FaqTranslations", back_populates="language")
    intent_utterance_translations = relationship("IntentUtteranceTranslations", back_populates="language")


class BotLanguages(Base):
    __tablename__ = 'bot_languages'
    id = Column(String(36), primary_key=True, default=generate_uuid)
    botId = Column(String(36), nullable=False, index=True)
    langId = Column(String(36), ForeignKey('languages.id'), nullable=False)
    createdAt = Column(DateTime, server_default=func.now())
    deletedAt = Column(DateTime)
    # createdBy, updatedBy, deletedBy can be added if you have a users table    
    # Relationships
    language = relationship("Languages", back_populates="bot_languages")


class FaqCategories(Base):
    __tablename__ = 'faq_categories'
    id = Column(String(36), primary_key=True, default=generate_uuid)
    botId = Column(String(36), nullable=False, index=True)
    name = Column(Text, nullable=False)
    description = Column(Text)
    createdAt = Column(DateTime, server_default=func.now())
    deletedAt = Column(DateTime)
    # createdBy, updatedBy, deletedBy can be added if you have a users table

    # Relationships
    faq_items = relationship("FaqItems", back_populates="category")


class FaqItems(Base):
    __tablename__ = 'faq_items'
    id = Column(String(36), primary_key=True, default=generate_uuid)
    botId = Column(String(36), nullable=False, index=True)
    flowId = Column(String(36))
    categoryId = Column(String(36), ForeignKey('faq_categories.id'), nullable=False)
    createdAt = Column(DateTime, server_default=func.now())
    deletedAt = Column(DateTime)
    
    # Relationships
    category = relationship("FaqCategories", back_populates="faq_items")
    translations = relationship("FaqTranslations", back_populates="faq_item")


class FaqTranslations(Base):
    __tablename__ = 'faq_translations'
    id = Column(String(36), primary_key=True, default=generate_uuid)
    faqId = Column(String(36), ForeignKey('faq_items.id'), nullable=False, index=True)
    langId = Column(String(36), ForeignKey('languages.id'), nullable=False, index=True)
    questions = Column(JSON, nullable=False)  # Array of strings
    answer = Column(Text, nullable=False)
    createdAt = Column(DateTime, server_default=func.now())
    updatedAt = Column(DateTime, onupdate=func.now())
    deletedAt = Column(DateTime)

    # Relationships
    faq_item = relationship("FaqItems", back_populates="translations")
    language = relationship("Languages", back_populates="faq_translations")


class IntentItems(Base):
    __tablename__ = 'intent_items'
    id = Column(String(36), primary_key=True, default=generate_uuid)
    botId = Column(String(36), nullable=False, index=True)
    flowId = Column(String(36))
    name = Column(Text, nullable=False)
    createdAt = Column(DateTime, server_default=func.now())
    updatedAt = Column(DateTime, onupdate=func.now())
    deletedAt = Column(DateTime)

    # Relationships
    utterances = relationship("IntentUtterances", back_populates="intent")
    entities = relationship("Entities", back_populates="intent")


class IntentUtterances(Base):
    __tablename__ = 'intent_utterances'
    id = Column(String(36), primary_key=True, default=generate_uuid)
    intentId = Column(String(36), ForeignKey('intent_items.id'), nullable=False)
    deletedAt = Column(DateTime)
    
    # Relationships
    intent = relationship("IntentItems", back_populates="utterances")
    translations = relationship("IntentUtteranceTranslations", back_populates="utterance")


class IntentUtteranceTranslations(Base):
    __tablename__ = 'intent_utterance_translations'
    id = Column(String(36), primary_key=True, default=generate_uuid)
    utteranceId = Column(String(36), ForeignKey('intent_utterances.id'), nullable=False, index=True)
    langId = Column(String(36), ForeignKey('languages.id'), nullable=False, index=True)
    text = Column(Text, nullable=False)
    entities = Column(JSON) # Optional NLU entity annotations
    createdAt = Column(DateTime, server_default=func.now())
    deletedAt = Column(DateTime)

    # Relationships
    utterance = relationship("IntentUtterances", back_populates="translations")
    language = relationship("Languages", back_populates="intent_utterance_translations")


class Entities(Base):
    __tablename__ = 'entities'
    id = Column(String(36), primary_key=True, default=generate_uuid)
    botId = Column(String(36), nullable=False, index=True)
    intentId = Column(String(36), ForeignKey('intent_items.id'), nullable=False, index=True)
    name = Column(Text, nullable=False)
    type = Column(Text, nullable=False)
    createdAt = Column(DateTime, server_default=func.now())
    updatedAt = Column(DateTime, onupdate=func.now())
    deletedAt = Column(DateTime)

    # Relationships
    intent = relationship("IntentItems", back_populates="entities")

# NOTE: The TrainingJob model is kept from the old schema as it is critical for the service's workflow.
# It was not part of the new schema description, but the service logic depends on it.
class TrainingJob(Base):
    __tablename__ = 'training_jobs'
    id = Column(String(36), primary_key=True)
    botId = Column(String(36), nullable=False)
    status = Column(String(50), default='pending') # Using String instead of Enum for broader compatibility
    modelUrl = Column(String(1024))
    errorMessage = Column(Text)
    updatedAt = Column(DateTime, default=func.now, onupdate=func.now)