# src/database/manager.py
import logging
from contextlib import contextmanager
from typing import Generator, List

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session, joinedload, subqueryload

from .models import (
    TrainingJob, Languages, BotLanguages, FaqTranslations, FaqItems,
    IntentUtteranceTranslations, IntentUtterances, IntentItems, Entities
)

logger = logging.getLogger(__name__)

class DatabaseManager:
    def __init__(self, db_uri: str):
        self.engine = create_engine(db_uri)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        logger.info("DatabaseManager initialized.")

    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session rolled back due to: {e}", exc_info=True)
            raise
        finally:
            session.close()

    def update_job_status(self, job_id: str, status: str, model_url: str = None, error_message: str = None):
        with self.get_session() as session:
            job = session.query(TrainingJob).filter_by(id=job_id).one_or_none()
            if not job:
                logger.error(f"Attempted to update a non-existent job with ID: {job_id}")
                return

            job.status = status
            if model_url:
                job.modelUrl = model_url
            if error_message:
                job.errorMessage = error_message
            logger.info(f"Updated job {job_id} to status '{status}'")

    def fetch_bot_languages(self, bot_id: str) -> List[Languages]:
        with self.get_session() as session:
            logger.info(f"Fetching configured languages for bot {bot_id}")
            languages = (
                session.query(Languages)
                .join(BotLanguages, Languages.id == BotLanguages.langId)
                .filter(BotLanguages.botId == bot_id)
                .all()
            )
            logger.info(f"Fetched {len(languages)} languages for bot {bot_id}")
            session.expunge_all()
            return languages

    def fetch_faq_translations(self, bot_id: str) -> List[FaqTranslations]:
        with self.get_session() as session:
            logger.info(f"Fetching FAQ translations for bot {bot_id}")
            translations = (
                session.query(FaqTranslations)
                .join(FaqItems, FaqTranslations.faqId == FaqItems.id)
                .options(
                    joinedload(FaqTranslations.faq_item),
                    joinedload(FaqTranslations.language)
                )
                .filter(FaqItems.botId == bot_id, FaqItems.deletedAt.is_(None), FaqTranslations.deletedAt.is_(None))
                .all()
            )
            logger.info(f"Fetched {len(translations)} FAQ translations for bot {bot_id}")
            session.expunge_all()
            return translations

    def fetch_intent_utterance_translations(self, bot_id: str) -> List[IntentUtteranceTranslations]:
        with self.get_session() as session:
            logger.info(f"Fetching Intent translations for bot {bot_id}")
            translations = (
                session.query(IntentUtteranceTranslations)
                .join(IntentUtterances, IntentUtteranceTranslations.utteranceId == IntentUtterances.id)
                .join(IntentItems, IntentUtterances.intentId == IntentItems.id)
                .options(
                    subqueryload(IntentUtteranceTranslations.utterance).joinedload(IntentUtterances.intent),
                    joinedload(IntentUtteranceTranslations.language)
                )
                .filter(IntentItems.botId == bot_id, IntentItems.deletedAt.is_(None), IntentUtteranceTranslations.deletedAt.is_(None))
                .all()
            )
            logger.info(f"Fetched {len(translations)} Intent utterance translations for bot {bot_id}")
            session.expunge_all()
            return translations
            
    def fetch_entities(self, bot_id: str) -> List[Entities]:
        with self.get_session() as session:
            logger.info(f"Fetching entities for bot {bot_id}")
            entities = (
                session.query(Entities)
                .filter(Entities.botId == bot_id, Entities.deletedAt.is_(None), Entities.deletedAt.is_(None))
                .all()
            )
            logger.info(f"Fetched {len(entities)} entities for bot {bot_id}")
            session.expunge_all()
            return entities