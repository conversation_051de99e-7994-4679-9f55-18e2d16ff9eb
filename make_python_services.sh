#!/bin/bash

# This script automates the setup for the training and rasa services.

echo "Starting service setup..."

# Activate the virtual environment
# echo "Activating virtual environment: rasa_env"
# activate-rasa

# Navigate to the training-service directory and install dependencies
echo "Navigating to training-service and installing dependencies..."
cd /home/<USER>/ng-neuratalk/training-service && pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "Failed to install dependencies for training-service."
    exit 1
fi

# Navigate to the rasa-server directory and install dependencies
echo "Navigating to rasa-server and installing dependencies..."
cd /home/<USER>/ng-neuratalk/rasa-server && pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "Failed to install dependencies for rasa-server."
    exit 1
fi

echo "Setup complete!"