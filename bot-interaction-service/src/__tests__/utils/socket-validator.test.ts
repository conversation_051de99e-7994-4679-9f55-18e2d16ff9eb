/**
 * Socket Validator Tests
 */

import { validateSocketPayloadHOC } from "../../utils/socket-validator";
import { SocketEvents } from "../../constants/socket.events";
import { z } from "zod";

// Mock socket
const mockSocket = {
  id: "socket-123",
  emit: jest.fn(),
};

// Mock logger
jest.mock("@neuratalk/common", () => ({
  logger: {
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

describe("validateSocketPayloadHOC", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("successful validation", () => {
    it("should call handler with parsed data when validation passes", async () => {
      const schema = z.object({
        message: z.string(),
        userId: z.string(),
      });

      const mockHandler = jest.fn().mockResolvedValue(undefined);
      const validator = validateSocketPayloadHOC(
        schema,
        mockSocket as any,
        SocketEvents.BOT_RESPONSE,
      );
      const validatedHandler = validator(mockHandler);

      const testData = { message: "Hello", userId: "user-123" };
      await validatedHandler(testData);

      expect(mockHandler).toHaveBeenCalledWith(testData);
      expect(mockSocket.emit).toHaveBeenCalledWith(SocketEvents.BOT_RESPONSE, { success: true });
    });

    it("should handle synchronous handlers", async () => {
      const schema = z.object({
        message: z.string(),
      });

      const mockHandler = jest.fn(); // Synchronous handler
      const validator = validateSocketPayloadHOC(
        schema,
        mockSocket as any,
        SocketEvents.BOT_RESPONSE,
      );
      const validatedHandler = validator(mockHandler);

      const testData = { message: "Hello" };
      await validatedHandler(testData);

      expect(mockHandler).toHaveBeenCalledWith(testData);
      expect(mockSocket.emit).toHaveBeenCalledWith(SocketEvents.BOT_RESPONSE, { success: true });
    });
  });

  describe("validation errors", () => {
    it("should emit validation error when schema validation fails", async () => {
      const schema = z.object({
        message: z.string(),
        userId: z.string(),
      });

      const mockHandler = jest.fn();
      const validator = validateSocketPayloadHOC(
        schema,
        mockSocket as any,
        SocketEvents.BOT_RESPONSE,
      );
      const validatedHandler = validator(mockHandler);

      const invalidData = { message: 123, userId: null }; // Invalid types
      await validatedHandler(invalidData);

      expect(mockHandler).not.toHaveBeenCalled();
      expect(mockSocket.emit).toHaveBeenCalledWith(SocketEvents.BOT_RESPONSE, {
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid payload format",
          details: expect.arrayContaining([
            expect.objectContaining({
              path: expect.any(String),
              message: expect.any(String),
            }),
          ]),
        },
      });
    });

    it("should format validation error details correctly", async () => {
      const schema = z.object({
        user: z.object({
          name: z.string(),
          age: z.number(),
        }),
      });

      const mockHandler = jest.fn();
      const validator = validateSocketPayloadHOC(
        schema,
        mockSocket as any,
        SocketEvents.BOT_RESPONSE,
      );
      const validatedHandler = validator(mockHandler);

      const invalidData = { user: { name: 123, age: "invalid" } };
      await validatedHandler(invalidData);

      expect(mockSocket.emit).toHaveBeenCalledWith(SocketEvents.BOT_RESPONSE, {
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid payload format",
          details: expect.arrayContaining([
            expect.objectContaining({
              path: "user.name",
              message: expect.any(String),
            }),
            expect.objectContaining({
              path: "user.age",
              message: expect.any(String),
            }),
          ]),
        },
      });
    });

    it("should handle missing required fields", async () => {
      const schema = z.object({
        message: z.string(),
        userId: z.string(),
      });

      const mockHandler = jest.fn();
      const validator = validateSocketPayloadHOC(
        schema,
        mockSocket as any,
        SocketEvents.BOT_RESPONSE,
      );
      const validatedHandler = validator(mockHandler);

      const incompleteData = { message: "Hello" }; // Missing userId
      await validatedHandler(incompleteData);

      expect(mockHandler).not.toHaveBeenCalled();
      expect(mockSocket.emit).toHaveBeenCalledWith(SocketEvents.BOT_RESPONSE, {
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid payload format",
          details: expect.arrayContaining([
            expect.objectContaining({
              path: "userId",
              message: expect.any(String),
            }),
          ]),
        },
      });
    });
  });

  describe("handler errors", () => {
    it("should emit internal error when handler throws non-ZodError", async () => {
      const schema = z.object({
        message: z.string(),
      });

      const mockHandler = jest.fn().mockRejectedValue(new Error("Handler failed"));
      const validator = validateSocketPayloadHOC(
        schema,
        mockSocket as any,
        SocketEvents.BOT_RESPONSE,
      );
      const validatedHandler = validator(mockHandler);

      const testData = { message: "Hello" };
      await validatedHandler(testData);

      expect(mockHandler).toHaveBeenCalledWith(testData);
      expect(mockSocket.emit).toHaveBeenCalledWith(SocketEvents.BOT_RESPONSE, {
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Something went wrong",
        },
      });
    });

    it("should emit internal error when synchronous handler throws", async () => {
      const schema = z.object({
        message: z.string(),
      });

      const mockHandler = jest.fn().mockImplementation(() => {
        throw new Error("Synchronous handler failed");
      });

      const validator = validateSocketPayloadHOC(
        schema,
        mockSocket as any,
        SocketEvents.BOT_RESPONSE,
      );
      const validatedHandler = validator(mockHandler);

      const testData = { message: "Hello" };
      await validatedHandler(testData);

      expect(mockHandler).toHaveBeenCalledWith(testData);
      expect(mockSocket.emit).toHaveBeenCalledWith(SocketEvents.BOT_RESPONSE, {
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message: "Something went wrong",
        },
      });
    });
  });

  describe("complex schemas", () => {
    it("should validate arrays correctly", async () => {
      const schema = z.object({
        messages: z.array(z.string()),
        count: z.number(),
      });

      const mockHandler = jest.fn();
      const validator = validateSocketPayloadHOC(
        schema,
        mockSocket as any,
        SocketEvents.BOT_RESPONSE,
      );
      const validatedHandler = validator(mockHandler);

      const validData = { messages: ["hello", "world"], count: 2 };
      await validatedHandler(validData);

      expect(mockHandler).toHaveBeenCalledWith(validData);
      expect(mockSocket.emit).toHaveBeenCalledWith(SocketEvents.BOT_RESPONSE, { success: true });
    });

    it("should validate optional fields correctly", async () => {
      const schema = z.object({
        message: z.string(),
        metadata: z
          .object({
            timestamp: z.number().optional(),
            source: z.string(),
          })
          .optional(),
      });

      const mockHandler = jest.fn();
      const validator = validateSocketPayloadHOC(
        schema,
        mockSocket as any,
        SocketEvents.BOT_RESPONSE,
      );
      const validatedHandler = validator(mockHandler);

      // Test with optional field present
      const dataWithOptional = {
        message: "Hello",
        metadata: { source: "web" },
      };
      await validatedHandler(dataWithOptional);

      expect(mockHandler).toHaveBeenCalledWith(dataWithOptional);

      // Test without optional field
      const dataWithoutOptional = { message: "Hello" };
      await validatedHandler(dataWithoutOptional);

      expect(mockHandler).toHaveBeenCalledWith(dataWithoutOptional);
    });
  });

  describe("edge cases", () => {
    it("should handle null data", async () => {
      const schema = z.object({
        message: z.string(),
      });

      const mockHandler = jest.fn();
      const validator = validateSocketPayloadHOC(
        schema,
        mockSocket as any,
        SocketEvents.BOT_RESPONSE,
      );
      const validatedHandler = validator(mockHandler);

      await validatedHandler(null);

      expect(mockHandler).not.toHaveBeenCalled();
      expect(mockSocket.emit).toHaveBeenCalledWith(SocketEvents.BOT_RESPONSE, {
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid payload format",
          details: expect.any(Array),
        },
      });
    });

    it("should handle undefined data", async () => {
      const schema = z.object({
        message: z.string(),
      });

      const mockHandler = jest.fn();
      const validator = validateSocketPayloadHOC(
        schema,
        mockSocket as any,
        SocketEvents.BOT_RESPONSE,
      );
      const validatedHandler = validator(mockHandler);

      await validatedHandler(undefined);

      expect(mockHandler).not.toHaveBeenCalled();
      expect(mockSocket.emit).toHaveBeenCalledWith(SocketEvents.BOT_RESPONSE, {
        success: false,
        error: {
          code: "VALIDATION_ERROR",
          message: "Invalid payload format",
          details: expect.any(Array),
        },
      });
    });
  });
});
