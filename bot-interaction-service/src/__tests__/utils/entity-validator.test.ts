/**
 * Entity Validator Tests
 */

import { validateEntityValue } from "../../utils/entity-validator";
import { EntityType } from "@neuratalk/bot-store";

describe("validateEntityValue", () => {
  describe("EMAIL validation", () => {
    it("should validate correct email format", () => {
      const result = validateEntityValue("<EMAIL>", EntityType.EMAIL);
      expect(result).toBe(true);
    });

    it("should reject invalid email format", () => {
      const result = validateEntityValue("invalid-email", EntityType.EMAIL);
      expect(result).toBe(false);
    });

    it("should reject email without @ symbol", () => {
      const result = validateEntityValue("testexample.com", EntityType.EMAIL);
      expect(result).toBe(false);
    });

    it("should reject email without domain", () => {
      const result = validateEntityValue("test@", EntityType.EMAIL);
      expect(result).toBe(false);
    });
  });

  describe("NUMBER validation", () => {
    it("should validate integer numbers", () => {
      const result = validateEntityValue("123", EntityType.NUMBER);
      expect(result).toBe(true);
    });

    it("should validate decimal numbers", () => {
      const result = validateEntityValue("123.45", EntityType.NUMBER);
      expect(result).toBe(true);
    });

    it("should validate negative numbers", () => {
      const result = validateEntityValue("-123", EntityType.NUMBER);
      expect(result).toBe(true);
    });

    it("should reject non-numeric strings", () => {
      const result = validateEntityValue("abc", EntityType.NUMBER);
      expect(result).toBe(false);
    });

    it("should reject mixed alphanumeric strings", () => {
      const result = validateEntityValue("123abc", EntityType.NUMBER);
      expect(result).toBe(false);
    });

    it("should validate zero", () => {
      const result = validateEntityValue("0", EntityType.NUMBER);
      expect(result).toBe(true);
    });
  });

  describe("DATE validation", () => {
    it("should validate ISO date format", () => {
      const result = validateEntityValue("2023-12-25", EntityType.DATE);
      expect(result).toBe(true);
    });

    it("should validate full ISO datetime format", () => {
      const result = validateEntityValue("2023-12-25T10:30:00Z", EntityType.DATE);
      expect(result).toBe(true);
    });

    it("should validate US date format", () => {
      const result = validateEntityValue("12/25/2023", EntityType.DATE);
      expect(result).toBe(true);
    });

    it("should validate timestamp", () => {
      const result = validateEntityValue("1703505000000", EntityType.DATE);
      expect(result).toBe(true);
    });

    it("should reject invalid date strings", () => {
      const result = validateEntityValue("invalid-date", EntityType.DATE);
      expect(result).toBe(false);
    });

    it("should reject impossible dates", () => {
      const result = validateEntityValue("2023-13-45", EntityType.DATE);
      expect(result).toBe(false);
    });
  });

  describe("REGEX validation", () => {
    it("should validate against custom regex pattern", () => {
      const metadata = { value: "^[A-Z]{2}\\d{4}$" };
      const result = validateEntityValue("AB1234", EntityType.REGEX, metadata);
      expect(result).toBe(true);
    });

    it("should reject values that do not match regex pattern", () => {
      const metadata = { value: "^[A-Z]{2}\\d{4}$" };
      const result = validateEntityValue("ab1234", EntityType.REGEX, metadata);
      expect(result).toBe(false);
    });

    it("should return false when no metadata is provided", () => {
      const result = validateEntityValue("AB1234", EntityType.REGEX);
      expect(result).toBe(false);
    });

    it("should return false when metadata value is empty", () => {
      const metadata = { value: "" };
      const result = validateEntityValue("AB1234", EntityType.REGEX, metadata);
      expect(result).toBe(false);
    });

    it("should return false when metadata value is not a string", () => {
      const metadata = { value: 123 as any };
      const result = validateEntityValue("AB1234", EntityType.REGEX, metadata);
      expect(result).toBe(false);
    });

    it("should handle invalid regex patterns gracefully", () => {
      const consoleSpy = jest.spyOn(console, "error").mockImplementation();
      const metadata = { value: "[invalid-regex" };

      const result = validateEntityValue("test", EntityType.REGEX, metadata);

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(
        "Invalid regex pattern:",
        "[invalid-regex",
        expect.any(Error),
      );

      consoleSpy.mockRestore();
    });

    it("should validate phone number pattern", () => {
      const metadata = { value: "^\\+?[1-9]\\d{1,14}$" };
      const result = validateEntityValue("+1234567890", EntityType.REGEX, metadata);
      expect(result).toBe(true);
    });

    it("should validate postal code pattern", () => {
      const metadata = { value: "^\\d{5}(-\\d{4})?$" };

      expect(validateEntityValue("12345", EntityType.REGEX, metadata)).toBe(true);
      expect(validateEntityValue("12345-6789", EntityType.REGEX, metadata)).toBe(true);
      expect(validateEntityValue("1234", EntityType.REGEX, metadata)).toBe(false);
    });
  });

  describe("TEXT validation", () => {
    it("should always return true for text type", () => {
      const result = validateEntityValue("any text value", EntityType.TEXT);
      expect(result).toBe(true);
    });

    it("should return true for empty string", () => {
      const result = validateEntityValue("", EntityType.TEXT);
      expect(result).toBe(true);
    });

    it("should return true for special characters", () => {
      const result = validateEntityValue("!@#$%^&*()", EntityType.TEXT);
      expect(result).toBe(true);
    });

    it("should return true for numbers as text", () => {
      const result = validateEntityValue("123", EntityType.TEXT);
      expect(result).toBe(true);
    });
  });

  describe("default case", () => {
    it("should return true for unknown entity types", () => {
      const result = validateEntityValue("test", "UNKNOWN_TYPE" as EntityType);
      expect(result).toBe(true);
    });
  });
});
