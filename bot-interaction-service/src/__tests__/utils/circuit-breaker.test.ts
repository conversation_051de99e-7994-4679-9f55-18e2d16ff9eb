/**
 * Circuit Breaker Tests
 */

import {
  Circuit<PERSON>reaker,
  CircuitBreakerManager,
  RetryManager,
  CircuitState,
} from "../../utils/circuit-breaker";

describe("CircuitBreaker", () => {
  let circuitBreaker: CircuitBreaker;

  beforeEach(() => {
    circuitBreaker = new CircuitBreaker("test-service", {
      failureThreshold: 3,
      recoveryTimeout: 1000,
      monitoringPeriod: 500,
    });
  });

  describe("constructor", () => {
    it("should initialize with default options", () => {
      const cb = new CircuitBreaker("test");
      expect(cb.getState()).toBe(CircuitState.CLOSED);
    });

    it("should initialize with custom options", () => {
      const cb = new CircuitBreaker("test", {
        failureThreshold: 5,
        recoveryTimeout: 2000,
      });
      expect(cb.getState()).toBe(CircuitState.CLOSED);
    });
  });

  describe("execute", () => {
    it("should execute operation successfully when circuit is closed", async () => {
      const operation = jest.fn().mockResolvedValue("success");

      const result = await circuitBreaker.execute(operation);

      expect(result).toBe("success");
      expect(operation).toHaveBeenCalled();
      expect(circuitBreaker.getState()).toBe(CircuitState.CLOSED);
    });

    it("should open circuit after failure threshold is reached", async () => {
      const operation = jest.fn().mockRejectedValue(new Error("Service error"));

      // Execute operations to reach failure threshold
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute(operation);
        } catch (error) {
          // Expected failures
        }
      }

      expect(circuitBreaker.getState()).toBe(CircuitState.OPEN);
    });

    it("should reject immediately when circuit is open", async () => {
      const operation = jest.fn().mockRejectedValue(new Error("Service error"));

      // Trigger circuit to open
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute(operation);
        } catch (error) {
          // Expected failures
        }
      }

      // Now circuit should be open and reject immediately
      await expect(circuitBreaker.execute(operation)).rejects.toThrow(
        "Circuit breaker is OPEN for test-service",
      );
    });

    it("should transition to half-open after recovery timeout", async () => {
      const operation = jest.fn().mockRejectedValue(new Error("Service error"));

      // Open the circuit
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute(operation);
        } catch (error) {
          // Expected failures
        }
      }

      expect(circuitBreaker.getState()).toBe(CircuitState.OPEN);

      // Wait for recovery timeout
      await new Promise((resolve) => setTimeout(resolve, 1100));

      // Next execution should transition to half-open
      operation.mockResolvedValue("success");
      const result = await circuitBreaker.execute(operation);

      expect(result).toBe("success");
      expect(circuitBreaker.getState()).toBe(CircuitState.HALF_OPEN);
    });

    it("should close circuit after successful executions in half-open state", async () => {
      const operation = jest.fn();

      // Open the circuit
      operation.mockRejectedValue(new Error("Service error"));
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute(operation);
        } catch (error) {
          // Expected failures
        }
      }

      // Wait for recovery timeout
      await new Promise((resolve) => setTimeout(resolve, 1100));

      // Execute successful operations to close circuit
      operation.mockResolvedValue("success");
      for (let i = 0; i < 3; i++) {
        await circuitBreaker.execute(operation);
      }

      expect(circuitBreaker.getState()).toBe(CircuitState.CLOSED);
    });

    it("should reopen circuit on failure in half-open state", async () => {
      const operation = jest.fn();

      // Open the circuit
      operation.mockRejectedValue(new Error("Service error"));
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute(operation);
        } catch (error) {
          // Expected failures
        }
      }

      // Wait for recovery timeout
      await new Promise((resolve) => setTimeout(resolve, 1100));

      // First success to transition to half-open
      operation.mockResolvedValue("success");
      await circuitBreaker.execute(operation);
      expect(circuitBreaker.getState()).toBe(CircuitState.HALF_OPEN);

      // Failure should reopen circuit
      operation.mockRejectedValue(new Error("Service error again"));
      try {
        await circuitBreaker.execute(operation);
      } catch (error) {
        // Expected failure
      }

      expect(circuitBreaker.getState()).toBe(CircuitState.OPEN);
    });
  });

  describe("getStats", () => {
    it("should return circuit breaker statistics", () => {
      const stats = circuitBreaker.getStats();

      expect(stats).toEqual({
        state: CircuitState.CLOSED,
        failureCount: 0,
        lastFailureTime: 0,
        serviceName: "test-service",
      });
    });

    it("should update stats after failures", async () => {
      const operation = jest.fn().mockRejectedValue(new Error("Service error"));

      try {
        await circuitBreaker.execute(operation);
      } catch (error) {
        // Expected failure
      }

      const stats = circuitBreaker.getStats();
      expect(stats.failureCount).toBe(1);
      expect(stats.lastFailureTime).toBeGreaterThan(0);
    });
  });
});

describe("CircuitBreakerManager", () => {
  let manager: CircuitBreakerManager;

  beforeEach(() => {
    // Reset singleton instance
    (CircuitBreakerManager as any).instance = undefined;
    manager = CircuitBreakerManager.getInstance();
  });

  describe("getInstance", () => {
    it("should return singleton instance", () => {
      const instance1 = CircuitBreakerManager.getInstance();
      const instance2 = CircuitBreakerManager.getInstance();

      expect(instance1).toBe(instance2);
    });
  });

  describe("getBreaker", () => {
    it("should create new circuit breaker for service", () => {
      const breaker = manager.getBreaker("service1");

      expect(breaker).toBeInstanceOf(CircuitBreaker);
      expect(breaker.getState()).toBe(CircuitState.CLOSED);
    });

    it("should return existing circuit breaker for service", () => {
      const breaker1 = manager.getBreaker("service1");
      const breaker2 = manager.getBreaker("service1");

      expect(breaker1).toBe(breaker2);
    });

    it("should create different breakers for different services", () => {
      const breaker1 = manager.getBreaker("service1");
      const breaker2 = manager.getBreaker("service2");

      expect(breaker1).not.toBe(breaker2);
    });

    it("should pass options to new circuit breaker", () => {
      const options = { failureThreshold: 10 };
      const breaker = manager.getBreaker("service1", options);

      expect(breaker).toBeInstanceOf(CircuitBreaker);
    });
  });

  describe("getAllStats", () => {
    it("should return stats for all circuit breakers", () => {
      const breaker1 = manager.getBreaker("service1");
      const breaker2 = manager.getBreaker("service2");

      const stats = manager.getAllStats();

      expect(stats).toHaveProperty("service1");
      expect(stats).toHaveProperty("service2");
      expect(stats.service1.serviceName).toBe("service1");
      expect(stats.service2.serviceName).toBe("service2");
    });

    it("should return empty object when no breakers exist", () => {
      const stats = manager.getAllStats();
      expect(stats).toEqual({});
    });
  });
});

describe("RetryManager", () => {
  describe("executeWithRetry", () => {
    it("should execute operation successfully on first try", async () => {
      const operation = jest.fn().mockResolvedValue("success");

      const result = await RetryManager.executeWithRetry(operation);

      expect(result).toBe("success");
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it("should retry operation on failure", async () => {
      const operation = jest
        .fn()
        .mockRejectedValueOnce(new Error("First failure"))
        .mockRejectedValueOnce(new Error("Second failure"))
        .mockResolvedValue("success");

      const result = await RetryManager.executeWithRetry(operation, 3);

      expect(result).toBe("success");
      expect(operation).toHaveBeenCalledTimes(3);
    });

    it("should throw last error after max retries", async () => {
      const error = new Error("Persistent failure");
      const operation = jest.fn().mockRejectedValue(error);

      await expect(RetryManager.executeWithRetry(operation, 2)).rejects.toThrow(
        "Persistent failure",
      );

      expect(operation).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });

    it("should use exponential backoff", async () => {
      const operation = jest
        .fn()
        .mockRejectedValueOnce(new Error("First failure"))
        .mockResolvedValue("success");

      const startTime = Date.now();
      await RetryManager.executeWithRetry(operation, 1, 100);
      const endTime = Date.now();

      expect(endTime - startTime).toBeGreaterThanOrEqual(100);
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it("should respect max delay", async () => {
      const operation = jest
        .fn()
        .mockRejectedValueOnce(new Error("First failure"))
        .mockResolvedValue("success");

      const startTime = Date.now();
      await RetryManager.executeWithRetry(operation, 1, 1000, 500);
      const endTime = Date.now();

      // Should use maxDelay (500ms) instead of calculated delay (1000ms)
      expect(endTime - startTime).toBeLessThan(1000);
      expect(endTime - startTime).toBeGreaterThanOrEqual(490); // Allow for timing precision
    });

    it("should handle zero retries", async () => {
      const error = new Error("Immediate failure");
      const operation = jest.fn().mockRejectedValue(error);

      await expect(RetryManager.executeWithRetry(operation, 0)).rejects.toThrow(
        "Immediate failure",
      );

      expect(operation).toHaveBeenCalledTimes(1);
    });
  });
});
