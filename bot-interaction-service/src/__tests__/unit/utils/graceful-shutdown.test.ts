/**
 * Graceful Shutdown Tests
 */

import { GracefulShutdown, create<PERSON>hu<PERSON><PERSON><PERSON><PERSON><PERSON>, ShutdownHandler } from '../../../utils/graceful-shutdown';

describe('GracefulShutdown', () => {
  let gracefulShutdown: GracefulShutdown;
  let processExitSpy: jest.SpyInstance;
  let processOnSpy: jest.SpyInstance;

  beforeEach(() => {
    // Reset singleton instance
    (GracefulShutdown as any).instance = undefined;
    gracefulShutdown = GracefulShutdown.getInstance();

    processExitSpy = jest.spyOn(process, 'exit').mockImplementation(() => {
      throw new Error('process.exit called');
    });

    processOnSpy = jest.spyOn(process, 'on').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
    processExitSpy.mockRestore();
    processOnSpy.mockRestore();
  });

  describe('getInstance', () => {
    it('should return singleton instance', () => {
      const instance1 = GracefulShutdown.getInstance();
      const instance2 = GracefulShutdown.getInstance();

      expect(instance1).toBe(instance2);
    });
  });

  describe('register', () => {
    it('should register shutdown handler', () => {
      const handler: ShutdownHandler = {
        name: 'test-handler',
        shutdown: jest.fn().mockResolvedValue(undefined)
      };

      gracefulShutdown.register(handler);

      expect((gracefulShutdown as any).handlers).toContain(handler);
    });

    it('should register multiple handlers', () => {
      const handler1: ShutdownHandler = {
        name: 'handler-1',
        shutdown: jest.fn().mockResolvedValue(undefined)
      };

      const handler2: ShutdownHandler = {
        name: 'handler-2',
        shutdown: jest.fn().mockResolvedValue(undefined)
      };

      gracefulShutdown.register(handler1);
      gracefulShutdown.register(handler2);

      expect((gracefulShutdown as any).handlers).toHaveLength(2);
    });
  });

  describe('init', () => {
    it('should setup signal handlers', () => {
      gracefulShutdown.init();

      expect(processOnSpy).toHaveBeenCalledWith('SIGTERM', expect.any(Function));
      expect(processOnSpy).toHaveBeenCalledWith('SIGINT', expect.any(Function));
      expect(processOnSpy).toHaveBeenCalledWith('uncaughtException', expect.any(Function));
      expect(processOnSpy).toHaveBeenCalledWith('unhandledRejection', expect.any(Function));
    });
  });

  describe('shutdown', () => {
    it('should execute all handlers on shutdown', async () => {
      const handler1 = jest.fn().mockResolvedValue(undefined);
      const handler2 = jest.fn().mockResolvedValue(undefined);

      gracefulShutdown.register({
        name: 'handler-1',
        shutdown: handler1
      });

      gracefulShutdown.register({
        name: 'handler-2',
        shutdown: handler2
      });

      gracefulShutdown.init();

      // Get the SIGTERM handler and call it
      const sigTermHandler = processOnSpy.mock.calls.find(call => call[0] === 'SIGTERM')[1];

      try {
        await sigTermHandler();
      } catch (error) {
        // Expected due to process.exit mock
      }

      expect(handler1).toHaveBeenCalled();
      expect(handler2).toHaveBeenCalled();
      expect(processExitSpy).toHaveBeenCalledWith(0);
    });

    it('should handle handler errors gracefully', async () => {
      const workingHandler = jest.fn().mockResolvedValue(undefined);
      const failingHandler = jest.fn().mockRejectedValue(new Error('Handler failed'));

      gracefulShutdown.register({
        name: 'working-handler',
        shutdown: workingHandler
      });

      gracefulShutdown.register({
        name: 'failing-handler',
        shutdown: failingHandler
      });

      gracefulShutdown.init();

      const sigTermHandler = processOnSpy.mock.calls.find(call => call[0] === 'SIGTERM')[1];

      try {
        await sigTermHandler();
      } catch (error) {
        // Expected due to process.exit mock
      }

      expect(workingHandler).toHaveBeenCalled();
      expect(failingHandler).toHaveBeenCalled();
      expect(processExitSpy).toHaveBeenCalledWith(0);
    });

    it('should handle handler timeouts', async () => {
      const slowHandler = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 15000)) // 15 seconds
      );

      gracefulShutdown.register({
        name: 'slow-handler',
        shutdown: slowHandler,
        timeout: 100 // 100ms timeout
      });

      gracefulShutdown.init();

      const sigTermHandler = processOnSpy.mock.calls.find(call => call[0] === 'SIGTERM')[1];

      try {
        await sigTermHandler();
      } catch (error) {
        // Expected due to process.exit mock
      }

      expect(slowHandler).toHaveBeenCalled();
      expect(processExitSpy).toHaveBeenCalledWith(0);
    });

    it('should exit with code 1 for uncaught exceptions', async () => {
      gracefulShutdown.init();

      const uncaughtExceptionHandler = processOnSpy.mock.calls.find(
        call => call[0] === 'uncaughtException'
      )[1];

      try {
        await uncaughtExceptionHandler(new Error('Test error'));
      } catch (error) {
        // Expected due to process.exit mock
      }

      expect(processExitSpy).toHaveBeenCalledWith(1);
    });

    it('should exit with code 1 for unhandled rejections', async () => {
      gracefulShutdown.init();

      const unhandledRejectionHandler = processOnSpy.mock.calls.find(
        call => call[0] === 'unhandledRejection'
      )[1];

      try {
        await unhandledRejectionHandler('Test reason', Promise.resolve());
      } catch (error) {
        // Expected due to process.exit mock
      }

      expect(processExitSpy).toHaveBeenCalledWith(1);
    });

    it('should ignore subsequent shutdown signals', async () => {
      const handler = jest.fn().mockResolvedValue(undefined);

      gracefulShutdown.register({
        name: 'test-handler',
        shutdown: handler
      });

      gracefulShutdown.init();

      const sigTermHandler = processOnSpy.mock.calls.find(call => call[0] === 'SIGTERM')[1];

      // First shutdown
      const firstShutdown = sigTermHandler().catch(() => {});
      
      // Second shutdown (should be ignored)
      const secondShutdown = sigTermHandler().catch(() => {});

      await Promise.all([firstShutdown, secondShutdown]);

      expect(handler).toHaveBeenCalledTimes(1);
    });
  });

  describe('isShutdownInProgress', () => {
    it('should return false initially', () => {
      expect(gracefulShutdown.isShutdownInProgress()).toBe(false);
    });

    it('should return true during shutdown', async () => {
      const handler = jest.fn().mockImplementation(() => 
        new Promise(resolve => {
          // Check status during shutdown
          expect(gracefulShutdown.isShutdownInProgress()).toBe(true);
          resolve(undefined);
        })
      );

      gracefulShutdown.register({
        name: 'test-handler',
        shutdown: handler
      });

      gracefulShutdown.init();

      const sigTermHandler = processOnSpy.mock.calls.find(call => call[0] === 'SIGTERM')[1];

      try {
        await sigTermHandler();
      } catch (error) {
        // Expected due to process.exit mock
      }

      expect(handler).toHaveBeenCalled();
    });
  });
});

describe('createShutdownHandler', () => {
  it('should create shutdown handler with name and function', () => {
    const shutdownFn = jest.fn().mockResolvedValue(undefined);
    const handler = createShutdownHandler('test-handler', shutdownFn);

    expect(handler.name).toBe('test-handler');
    expect(handler.shutdown).toBe(shutdownFn);
    expect(handler.timeout).toBeUndefined();
  });

  it('should create shutdown handler with timeout', () => {
    const shutdownFn = jest.fn().mockResolvedValue(undefined);
    const handler = createShutdownHandler('test-handler', shutdownFn, 5000);

    expect(handler.name).toBe('test-handler');
    expect(handler.shutdown).toBe(shutdownFn);
    expect(handler.timeout).toBe(5000);
  });
});