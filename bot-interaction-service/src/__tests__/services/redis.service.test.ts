/**
 * Redis Service Tests
 */

import { RedisService } from "../../services/redis.service";
import { createClient } from "redis";

// Mock Redis client
jest.mock("redis", () => ({
  createClient: jest.fn(),
}));

describe("RedisService", () => {
  let redisService: RedisService;
  let mockClient: any;

  beforeEach(() => {
    mockClient = {
      connect: jest.fn(),
      quit: jest.fn(),
      on: jest.fn(),
      get: jest.fn(),
      set: jest.fn(),
      setEx: jest.fn(),
      del: jest.fn(),
      ping: jest.fn(),
      flushDb: jest.fn(),
    };

    (createClient as jest.Mock).mockReturnValue(mockClient);
    redisService = new RedisService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("constructor", () => {
    it("should create Redis client with correct configuration", () => {
      expect(createClient).toHaveBeenCalledWith({
        url: expect.any(String),
        socket: {
          host: expect.any(String),
          port: expect.any(Number),
        },
        password: undefined,
        database: expect.any(Number),
      });
    });

    it("should setup event handlers", () => {
      expect(mockClient.on).toHaveBeenCalledWith("connect", expect.any(Function));
      expect(mockClient.on).toHaveBeenCalledWith("error", expect.any(Function));
      expect(mockClient.on).toHaveBeenCalledWith("end", expect.any(Function));
    });
  });

  describe("connect", () => {
    it("should connect successfully", async () => {
      mockClient.connect.mockResolvedValue(undefined);

      await redisService.connect();

      expect(mockClient.connect).toHaveBeenCalled();
    });

    it("should throw error on connection failure", async () => {
      const error = new Error("Connection failed");
      mockClient.connect.mockRejectedValue(error);

      await expect(redisService.connect()).rejects.toThrow("Connection failed");
    });
  });

  describe("disconnect", () => {
    it("should disconnect successfully", async () => {
      mockClient.quit.mockResolvedValue(undefined);

      await redisService.disconnect();

      expect(mockClient.quit).toHaveBeenCalled();
    });

    it("should handle disconnect errors gracefully", async () => {
      mockClient.quit.mockRejectedValue(new Error("Disconnect failed"));

      await expect(redisService.disconnect()).resolves.toBeUndefined();
    });
  });

  describe("clearAll", () => {
    it("should clear all keys successfully", async () => {
      mockClient.flushDb.mockResolvedValue(undefined);

      await redisService.clearAll();

      expect(mockClient.flushDb).toHaveBeenCalled();
    });

    it("should handle clear errors gracefully", async () => {
      mockClient.flushDb.mockRejectedValue(new Error("Clear failed"));

      await expect(redisService.clearAll()).resolves.toBeUndefined();
    });
  });

  describe("conversation context management", () => {
    const mockContext = {
      chatConversationId: "conv-123",
      sessionStartedAt: new Date(),
      lastActivityAt: new Date(),
      expiresAt: new Date(),
      userId: "user-123",
      botId: "bot-123",
      invokedIntent: null,
      metadata: {
        sessionTimeout: 30, // Example value
      },
      journeyContext: {
        globalContext: {
          sessionData: {},
          userInputHistory: {},
        },
      },
    };

    describe("getConversationContext", () => {
      it("should return parsed context when found", async () => {
        mockClient.get.mockResolvedValue(JSON.stringify(mockContext));

        const result = await redisService.getConversationContext("conv-123");

        expect(mockClient.get).toHaveBeenCalledWith("chatbot:context:conv-123");
        expect(result).toEqual(
          expect.objectContaining({
            chatConversationId: "conv-123",
            userId: "user-123",
            botId: "bot-123",
          }),
        );
      });

      it("should return null when context not found", async () => {
        mockClient.get.mockResolvedValue(null);

        const result = await redisService.getConversationContext("conv-123");

        expect(result).toBeNull();
      });

      it("should return null on error", async () => {
        mockClient.get.mockRejectedValue(new Error("Redis error"));

        const result = await redisService.getConversationContext("conv-123");

        expect(result).toBeNull();
      });
    });

    describe("setConversationContext", () => {
      it("should set context successfully", async () => {
        mockClient.setEx.mockResolvedValue("OK");

        const result = await redisService.setConversationContext(mockContext);

        expect(mockClient.setEx).toHaveBeenCalledWith(
          "chatbot:context:conv-123",
          1800, // 30 minutes default TTL
          expect.any(String),
        );
        expect(result).toBe(true);
      });

      it("should use custom TTL when provided", async () => {
        mockClient.setEx.mockResolvedValue("OK");

        await redisService.setConversationContext(mockContext, 60);

        expect(mockClient.setEx).toHaveBeenCalledWith(
          "chatbot:context:conv-123",
          3600, // 60 minutes
          expect.any(String),
        );
      });

      it("should return false on error", async () => {
        mockClient.setEx.mockRejectedValue(new Error("Redis error"));

        const result = await redisService.setConversationContext(mockContext);

        expect(result).toBe(false);
      });
    });

    describe("updateConversationContext", () => {
      it("should update existing context", async () => {
        mockClient.get.mockResolvedValue(JSON.stringify(mockContext));
        mockClient.setEx.mockResolvedValue("OK");

        const updates = { userId: "new-user-123" };
        const result = await redisService.updateConversationContext("conv-123", updates);

        expect(result).toBe(true);
      });

      it("should return false when context does not exist", async () => {
        mockClient.get.mockResolvedValue(null);

        const result = await redisService.updateConversationContext("conv-123", {});

        expect(result).toBe(false);
      });
    });

    describe("deleteConversationContext", () => {
      it("should delete context successfully", async () => {
        mockClient.del.mockResolvedValue(1);

        const result = await redisService.deleteConversationContext("conv-123");

        expect(mockClient.del).toHaveBeenCalledWith("chatbot:context:conv-123");
        expect(result).toBe(true);
      });

      it("should return false when key does not exist", async () => {
        mockClient.del.mockResolvedValue(0);

        const result = await redisService.deleteConversationContext("conv-123");

        expect(result).toBe(false);
      });
    });
  });

  describe("flow caching", () => {
    const mockFlow = { id: "flow-123", name: "Test Flow" };

    describe("getFlow", () => {
      it("should return parsed flow when found", async () => {
        mockClient.get.mockResolvedValue(JSON.stringify(mockFlow));

        const result = await redisService.getFlow("flow-123");

        expect(mockClient.get).toHaveBeenCalledWith("chatbot:flow:flow-123");
        expect(result).toEqual(mockFlow);
      });

      it("should return null when flow not found", async () => {
        mockClient.get.mockResolvedValue(null);

        const result = await redisService.getFlow("flow-123");

        expect(result).toBeNull();
      });
    });

    describe("setFlow", () => {
      it("should cache flow successfully", async () => {
        mockClient.setEx.mockResolvedValue("OK");

        const result = await redisService.setFlow("flow-123", mockFlow);

        expect(mockClient.setEx).toHaveBeenCalledWith(
          "chatbot:flow:flow-123",
          3600, // 60 minutes default
          JSON.stringify(mockFlow),
        );
        expect(result).toBe(true);
      });

      it("should use custom TTL", async () => {
        mockClient.setEx.mockResolvedValue("OK");

        await redisService.setFlow("flow-123", mockFlow, 120);

        expect(mockClient.setEx).toHaveBeenCalledWith(
          "chatbot:flow:flow-123",
          7200, // 120 minutes
          JSON.stringify(mockFlow),
        );
      });
    });

    describe("invalidateFlow", () => {
      it("should invalidate flow cache", async () => {
        mockClient.del.mockResolvedValue(1);

        const result = await redisService.invalidateFlow("flow-123");

        expect(mockClient.del).toHaveBeenCalledWith("chatbot:flow:flow-123");
        expect(result).toBe(true);
      });
    });
  });

  describe("general cache operations", () => {
    describe("get", () => {
      it("should get value successfully", async () => {
        mockClient.get.mockResolvedValue("test-value");

        const result = await redisService.get("test-key");

        expect(mockClient.get).toHaveBeenCalledWith("chatbot:test-key");
        expect(result).toBe("test-value");
      });

      it("should return null on error", async () => {
        mockClient.get.mockRejectedValue(new Error("Redis error"));

        const result = await redisService.get("test-key");

        expect(result).toBeNull();
      });
    });

    describe("set", () => {
      it("should set value without TTL", async () => {
        mockClient.set.mockResolvedValue("OK");

        const result = await redisService.set("test-key", "test-value");

        expect(mockClient.set).toHaveBeenCalledWith("chatbot:test-key", "test-value");
        expect(result).toBe(true);
      });

      it("should set value with TTL", async () => {
        mockClient.setEx.mockResolvedValue("OK");

        const result = await redisService.set("test-key", "test-value", 3600);

        expect(mockClient.setEx).toHaveBeenCalledWith("chatbot:test-key", 3600, "test-value");
        expect(result).toBe(true);
      });
    });

    describe("del", () => {
      it("should delete key successfully", async () => {
        mockClient.del.mockResolvedValue(1);

        const result = await redisService.del("test-key");

        expect(mockClient.del).toHaveBeenCalledWith("chatbot:test-key");
        expect(result).toBe(true);
      });
    });

    describe("getOrSet", () => {
      it("should return cached data when available", async () => {
        const cachedData = { test: "cached" };
        mockClient.get.mockResolvedValue(JSON.stringify(cachedData));

        const fetchFunction = jest.fn();
        const result = await redisService.getOrSet("test-key", fetchFunction);

        expect(mockClient.get).toHaveBeenCalledWith("chatbot:test-key");
        expect(fetchFunction).not.toHaveBeenCalled();
        expect(result).toEqual(cachedData);
      });

      it("should fetch and cache data when not in cache", async () => {
        const fetchedData = { test: "fetched" };
        mockClient.get.mockResolvedValue(null);
        mockClient.setEx.mockResolvedValue("OK");

        const fetchFunction = jest.fn().mockResolvedValue(fetchedData);
        const result = await redisService.getOrSet("test-key", fetchFunction);

        expect(fetchFunction).toHaveBeenCalled();
        expect(mockClient.setEx).toHaveBeenCalledWith(
          "chatbot:test-key",
          3600,
          JSON.stringify(fetchedData),
        );
        expect(result).toEqual(fetchedData);
      });

      it("should force refresh when requested", async () => {
        const fetchedData = { test: "fetched" };
        mockClient.setEx.mockResolvedValue("OK");

        const fetchFunction = jest.fn().mockResolvedValue(fetchedData);
        const result = await redisService.getOrSet("test-key", fetchFunction, 3600, true);

        expect(mockClient.get).not.toHaveBeenCalled();
        expect(fetchFunction).toHaveBeenCalled();
        expect(result).toEqual(fetchedData);
      });
    });
  });

  describe("healthCheck", () => {
    it("should return healthy status when ping succeeds", async () => {
      mockClient.ping.mockResolvedValue("PONG");

      // Mock Date.now() to return predictable values
      const mockDateNow = jest.spyOn(Date, "now");
      mockDateNow.mockReturnValueOnce(1000).mockReturnValueOnce(1050); // 50ms difference

      const result = await redisService.healthCheck();

      expect(mockClient.ping).toHaveBeenCalled();
      expect(result.status).toBe("healthy");
      expect(result.responseTime).toBeGreaterThan(0);

      mockDateNow.mockRestore();
    });

    it("should return unhealthy status when ping fails", async () => {
      mockClient.ping.mockRejectedValue(new Error("Connection failed"));

      const result = await redisService.healthCheck();

      expect(result.status).toBe("unhealthy");
      expect(result.responseTime).toBeUndefined();
    });
  });

  describe("connected getter", () => {
    it("should return connection status", () => {
      expect(redisService.connected).toBe(false);
    });
  });
});
