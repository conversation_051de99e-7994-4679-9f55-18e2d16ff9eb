/**
 * Application Repository Service Tests
 */

import { ApplicationRepository } from '../../services/application-repository.service';
import { DatabaseService } from '../../services/database.service';
import { RedisService } from '../../services/redis.service';
import { EntityType, BotStatus } from '@neuratalk/bot-store';

// Mock dependencies
jest.mock('../../services/database.service');
jest.mock('../../services/redis.service');

describe('ApplicationRepository', () => {
  let applicationRepository: ApplicationRepository;
  let mockDatabaseService: jest.Mocked<DatabaseService>;
  let mockRedisService: jest.Mocked<RedisService>;

  beforeEach(() => {
    mockDatabaseService = {
      getEntityByIdAndBotId: jest.fn(),
      getFlow: jest.fn(),
      getFlowsByBot: jest.fn(),
      getIntentItemByName: jest.fn(),
      getIntentItemWithFlow: jest.fn(),
      getFaqItemWithFlow: jest.fn(),
      getBot: jest.fn(),
      getFaqTranslation: jest.fn()
    } as any;

    mockRedisService = {
      getOrSet: jest.fn()
    } as any;

    applicationRepository = new ApplicationRepository(mockDatabaseService, mockRedisService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getEntityByIdAndBotId', () => {
    it('should use cache-aside pattern to get entity', async () => {
      const mockEntity = { id: 'entity-123', botId: 'bot-123', name: 'Test Entity', type: EntityType.TEXT, createdBy: 'test', updatedBy: 'test' };
      mockRedisService.getOrSet.mockResolvedValue(mockEntity);

      const result = await applicationRepository.getEntityByIdAndBotId('bot-123', 'entity-123');

      expect(mockRedisService.getOrSet).toHaveBeenCalledWith(
        'entity:bot-123:entity-123',
        expect.any(Function),
        3600,
        false
      );
      expect(result).toEqual(mockEntity);
    });

    it('should force refresh when requested', async () => {
      const mockEntity = { id: 'entity-123', botId: 'bot-123', name: 'Test Entity', type: EntityType.TEXT, createdBy: 'test', updatedBy: 'test' };
      mockRedisService.getOrSet.mockResolvedValue(mockEntity);

      await applicationRepository.getEntityByIdAndBotId('bot-123', 'entity-123', true);

      expect(mockRedisService.getOrSet).toHaveBeenCalledWith(
        'entity:bot-123:entity-123',
        expect.any(Function),
        3600,
        true
      );
    });

    it('should call database service when cache misses', async () => {
      const mockEntity = { id: 'entity-123', botId: 'bot-123', name: 'Test Entity', type: EntityType.TEXT, createdBy: 'test', updatedBy: 'test' };
      mockDatabaseService.getEntityByIdAndBotId.mockResolvedValue(mockEntity);

      // Mock getOrSet to call the fetch function
      mockRedisService.getOrSet.mockImplementation(async (key, fetchFn) => {
        return await fetchFn();
      });

      const result = await applicationRepository.getEntityByIdAndBotId('bot-123', 'entity-123');

      expect(mockDatabaseService.getEntityByIdAndBotId).toHaveBeenCalledWith('bot-123', 'entity-123');
      expect(result).toEqual(mockEntity);
    });
  });

  describe('getFlow', () => {
    it('should use cache-aside pattern to get flow', async () => {
      const mockFlow = { id: 'flow-123', name: 'Test Flow', botId: 'bot-123', appId: 'app-123', createdBy: 'test', updatedBy: 'test' };
      mockRedisService.getOrSet.mockResolvedValue(mockFlow);

      const result = await applicationRepository.getFlow('flow-123');

      expect(mockRedisService.getOrSet).toHaveBeenCalledWith(
        'flow:flow-123',
        expect.any(Function),
        3600,
        false
      );
      expect(result).toEqual(mockFlow);
    });

    it('should force refresh when requested', async () => {
      const mockFlow = { id: 'flow-123', name: 'Test Flow', botId: 'bot-123', appId: 'app-123', createdBy: 'test', updatedBy: 'test' };
      mockRedisService.getOrSet.mockResolvedValue(mockFlow);

      await applicationRepository.getFlow('flow-123', true);

      expect(mockRedisService.getOrSet).toHaveBeenCalledWith(
        'flow:flow-123',
        expect.any(Function),
        3600,
        true
      );
    });

    it('should call database service when cache misses', async () => {
      const mockFlow = { id: 'flow-123', name: 'Test Flow', botId: 'bot-123', appId: 'app-123', createdBy: 'test', updatedBy: 'test' };
      mockDatabaseService.getFlow.mockResolvedValue(mockFlow);

      mockRedisService.getOrSet.mockImplementation(async (key, fetchFn) => {
        return await fetchFn();
      });

      const result = await applicationRepository.getFlow('flow-123');

      expect(mockDatabaseService.getFlow).toHaveBeenCalledWith('flow-123');
      expect(result).toEqual(mockFlow);
    });
  });

  describe('getFlowsByBot', () => {
    it('should use cache-aside pattern to get flows by bot', async () => {
      const mockFlows = [
        { id: 'flow-1', botId: 'bot-123', name: 'Flow 1', appId: 'app-1', createdBy: 'test', updatedBy: 'test' },
        { id: 'flow-2', botId: 'bot-123', name: 'Flow 2', appId: 'app-2', createdBy: 'test', updatedBy: 'test' }
      ];
      mockRedisService.getOrSet.mockResolvedValue(mockFlows);

      const result = await applicationRepository.getFlowsByBot('bot-123');

      expect(mockRedisService.getOrSet).toHaveBeenCalledWith(
        'flows:bot:bot-123',
        expect.any(Function),
        3600,
        false
      );
      expect(result).toEqual(mockFlows);
    });

    it('should call database service when cache misses', async () => {
      const mockFlows = [
        { id: 'flow-1', botId: 'bot-123', name: 'Flow 1', appId: 'app-1', createdBy: 'test', updatedBy: 'test' }
      ];
      mockDatabaseService.getFlowsByBot.mockResolvedValue(mockFlows);

      mockRedisService.getOrSet.mockImplementation(async (key, fetchFn) => {
        return await fetchFn();
      });

      const result = await applicationRepository.getFlowsByBot('bot-123');

      expect(mockDatabaseService.getFlowsByBot).toHaveBeenCalledWith('bot-123');
      expect(result).toEqual(mockFlows);
    });
  });

  describe('getIntentItemByName', () => {
    it('should use cache-aside pattern to get intent item by name', async () => {
      const mockIntentItem = { id: 'intent-123', name: 'greeting', botId: 'bot-123', createdBy: 'test', updatedBy: 'test' };
      mockRedisService.getOrSet.mockResolvedValue(mockIntentItem);

      const result = await applicationRepository.getIntentItemByName('bot-123', 'greeting');

      expect(mockRedisService.getOrSet).toHaveBeenCalledWith(
        'intentItemByName:bot-123:greeting',
        expect.any(Function),
        3600,
        false
      );
      expect(result).toEqual(mockIntentItem);
    });

    it('should call database service when cache misses', async () => {
      const mockIntentItem = { id: 'intent-123', name: 'greeting', botId: 'bot-123', createdBy: 'test', updatedBy: 'test' };
      mockDatabaseService.getIntentItemByName.mockResolvedValue(mockIntentItem);

      mockRedisService.getOrSet.mockImplementation(async (key, fetchFn) => {
        return await fetchFn();
      });

      const result = await applicationRepository.getIntentItemByName('bot-123', 'greeting');

      expect(mockDatabaseService.getIntentItemByName).toHaveBeenCalledWith('bot-123', 'greeting');
      expect(result).toEqual(mockIntentItem);
    });
  });

  describe('getIntentItemWithFlow', () => {
    it('should use cache-aside pattern to get intent item with flow', async () => {
      const mockIntentItem = {
        id: 'intent-123',
        flow: { id: 'flow-123', name: 'Test Flow', botId: 'bot-123', appId: 'app-123', createdBy: 'test', updatedBy: 'test' } as any,
        botId: 'bot-123', name: 'Test Intent', createdBy: 'test', updatedBy: 'test'
      };
      mockRedisService.getOrSet.mockResolvedValue(mockIntentItem);

      const result = await applicationRepository.getIntentItemWithFlow('intent-123');

      expect(mockRedisService.getOrSet).toHaveBeenCalledWith(
        'intentItemWithFlow:intent-123',
        expect.any(Function),
        3600,
        false
      );
      expect(result).toEqual(mockIntentItem);
    });

    it('should call database service when cache misses', async () => {
      const mockIntentItem = {
        id: 'intent-123',
        flow: { id: 'flow-123', name: 'Test Flow', botId: 'bot-123', appId: 'app-123', createdBy: 'test', updatedBy: 'test' } as any,
        botId: 'bot-123', name: 'Test Intent', createdBy: 'test', updatedBy: 'test'
      };
      mockDatabaseService.getIntentItemWithFlow.mockResolvedValue(mockIntentItem);

      mockRedisService.getOrSet.mockImplementation(async (key, fetchFn) => {
        return await fetchFn();
      });

      const result = await applicationRepository.getIntentItemWithFlow('intent-123');

      expect(mockDatabaseService.getIntentItemWithFlow).toHaveBeenCalledWith('intent-123');
      expect(result).toEqual(mockIntentItem);
    });
  });

  describe('getFaqItemWithFlow', () => {
    it('should use cache-aside pattern to get FAQ item with flow', async () => {
      const mockFaqItem = {
        id: 'faq-123',
        flow: { id: 'flow-123', name: 'Test Flow', botId: 'bot-123', appId: 'app-123', createdBy: 'test', updatedBy: 'test' } as any,
        botId: 'bot-123', categoryId: 'cat-123', createdBy: 'test', updatedBy: 'test'
      };
      mockRedisService.getOrSet.mockResolvedValue(mockFaqItem);

      const result = await applicationRepository.getFaqItemWithFlow('faq-123');

      expect(mockRedisService.getOrSet).toHaveBeenCalledWith(
        'faqItemWithFlow:faq-123',
        expect.any(Function),
        3600,
        false
      );
      expect(result).toEqual(mockFaqItem);
    });

    it('should call database service when cache misses', async () => {
      const mockFaqItem = {
        id: 'faq-123',
        flow: { id: 'flow-123', name: 'Test Flow', botId: 'bot-123', appId: 'app-123', createdBy: 'test', updatedBy: 'test' } as any,
        botId: 'bot-123', categoryId: 'cat-123', createdBy: 'test', updatedBy: 'test'
      };
      mockDatabaseService.getFaqItemWithFlow.mockResolvedValue(mockFaqItem);

      mockRedisService.getOrSet.mockImplementation(async (key, fetchFn) => {
        return await fetchFn();
      });

      const result = await applicationRepository.getFaqItemWithFlow('faq-123');

      expect(mockDatabaseService.getFaqItemWithFlow).toHaveBeenCalledWith('faq-123');
      expect(result).toEqual(mockFaqItem);
    });
  });

  describe('getBot', () => {
    it('should use cache-aside pattern to get bot', async () => {
      const mockBot = { id: 'bot-123', name: 'Test Bot', status: BotStatus.ACTIVE, createdAt: new Date(), updatedAt: new Date() };
      mockRedisService.getOrSet.mockResolvedValue(mockBot);

      const result = await applicationRepository.getBot('bot-123');

      expect(mockRedisService.getOrSet).toHaveBeenCalledWith(
        'bot:bot-123',
        expect.any(Function),
        3600,
        false
      );
      expect(result).toEqual(mockBot);
    });

    it('should force refresh when requested', async () => {
      const mockBot = { id: 'bot-123', name: 'Test Bot', status: BotStatus.ACTIVE, createdAt: new Date(), updatedAt: new Date() };
      mockRedisService.getOrSet.mockResolvedValue(mockBot);

      await applicationRepository.getBot('bot-123', true);

      expect(mockRedisService.getOrSet).toHaveBeenCalledWith(
        'bot:bot-123',
        expect.any(Function),
        3600,
        true
      );
    });

    it('should call database service when cache misses', async () => {
      const mockBot = { id: 'bot-123', name: 'Test Bot', status: BotStatus.ACTIVE, createdAt: new Date(), updatedAt: new Date() };
      mockDatabaseService.getBot.mockResolvedValue(mockBot);

      mockRedisService.getOrSet.mockImplementation(async (key, fetchFn) => {
        return await fetchFn();
      });

      const result = await applicationRepository.getBot('bot-123');

      expect(mockDatabaseService.getBot).toHaveBeenCalledWith('bot-123', true);
      expect(result).toEqual(mockBot);
    });

    it('should pass correct isPublished parameter to database service', async () => {
      const mockBot = { id: 'bot-123', name: 'Test Bot', status: BotStatus.ACTIVE, createdAt: new Date(), updatedAt: new Date() };
      mockDatabaseService.getBot.mockResolvedValue(mockBot);

      mockRedisService.getOrSet.mockImplementation(async (key, fetchFn) => {
        return await fetchFn();
      });

      // Test with forceRefresh = false
      await applicationRepository.getBot('bot-123', false);
      expect(mockDatabaseService.getBot).toHaveBeenCalledWith('bot-123', true);

      // Test with forceRefresh = true
      await applicationRepository.getBot('bot-123', true);
      expect(mockDatabaseService.getBot).toHaveBeenCalledWith('bot-123', false);
    });
  });

  describe('getFaqTranslation', () => {
    it('should use cache-aside pattern to get FAQ translation', async () => {
      const mockTranslation = {
        id: 'translation-123',
        faqId: 'faq-123',
        langCode: 'en',
        langId: 'lang-123',
        questions: ['Question 1'],
        answer: 'Answer 1',
        createdBy: 'test',
        updatedBy: 'test'
      };
      mockRedisService.getOrSet.mockResolvedValue(mockTranslation);

      const result = await applicationRepository.getFaqTranslation('faq-123', 'en');

      expect(mockRedisService.getOrSet).toHaveBeenCalledWith(
        'faq_translation:faq-123:en',
        expect.any(Function),
        3600,
        false
      );
      expect(result).toEqual(mockTranslation);
    });

    it('should force refresh when requested', async () => {
      const mockTranslation = {
        id: 'translation-123',
        faqId: 'faq-123',
        langCode: 'en',
        langId: 'lang-123',
        questions: ['Question 1'],
        answer: 'Answer 1',
        createdBy: 'test',
        updatedBy: 'test'
      };
      mockRedisService.getOrSet.mockResolvedValue(mockTranslation);

      await applicationRepository.getFaqTranslation('faq-123', 'en', true);

      expect(mockRedisService.getOrSet).toHaveBeenCalledWith(
        'faq_translation:faq-123:en',
        expect.any(Function),
        3600,
        true
      );
    });

    it('should call database service when cache misses', async () => {
      const mockTranslation = {
        id: 'translation-123',
        faqId: 'faq-123',
        langCode: 'en',
        langId: 'lang-123',
        questions: ['Question 1'],
        answer: 'Answer 1',
        createdBy: 'test',
        updatedBy: 'test'
      };
      mockDatabaseService.getFaqTranslation.mockResolvedValue(mockTranslation);

      mockRedisService.getOrSet.mockImplementation(async (key, fetchFn) => {
        return await fetchFn();
      });

      const result = await applicationRepository.getFaqTranslation('faq-123', 'en');

      expect(mockDatabaseService.getFaqTranslation).toHaveBeenCalledWith('faq-123', 'en');
      expect(result).toEqual(mockTranslation);
    });
  });
});
