/**
 * Database Service Tests
 */

import { DatabaseService } from '../../services/database.service';
import { DatabaseConnection, BotStatus } from '@neuratalk/bot-store';
import { Op } from 'sequelize';

// Mock dependencies
jest.mock('@neuratalk/bot-store');

describe('DatabaseService', () => {
  let databaseService: DatabaseService;
  let mockDatabaseConnection: jest.Mocked<DatabaseConnection>;
  let mockModels: any;

  beforeEach(() => {
    mockModels = {
      Flow: {
        findByPk: jest.fn(),
        findAll: jest.fn()
      },
      IntentItems: {
        findOne: jest.fn(),
        findByPk: jest.fn()
      },
      FaqItems: {
        findByPk: jest.fn()
      },
      FaqTranslation: {
        findOne: jest.fn()
      },
      Entities: {
        findOne: jest.fn()
      },
      Bot: {
        findOne: jest.fn()
      },
      TrainingJob: {},
      Language: {}
    };

    mockDatabaseConnection = {
      models: mockModels
    } as any;

    databaseService = new DatabaseService(mockDatabaseConnection);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getFlow', () => {
    it('should return flow when found', async () => {
      const mockFlow = { id: 'flow-123', name: 'Test Flow' };
      mockModels.Flow.findByPk.mockResolvedValue(mockFlow);

      const result = await databaseService.getFlow('flow-123');

      expect(mockModels.Flow.findByPk).toHaveBeenCalledWith('flow-123');
      expect(result).toEqual(mockFlow);
    });

    it('should return null when flow not found', async () => {
      mockModels.Flow.findByPk.mockResolvedValue(null);

      const result = await databaseService.getFlow('flow-123');

      expect(result).toBeNull();
    });

    it('should return null on error', async () => {
      mockModels.Flow.findByPk.mockRejectedValue(new Error('Database error'));

      const result = await databaseService.getFlow('flow-123');

      expect(result).toBeNull();
    });
  });

  describe('getFlowsByBot', () => {
    it('should return flows for bot', async () => {
      const mockFlows = [
        { id: 'flow-1', botId: 'bot-123' },
        { id: 'flow-2', botId: 'bot-123' }
      ];
      mockModels.Flow.findAll.mockResolvedValue(mockFlows);

      const result = await databaseService.getFlowsByBot('bot-123');

      expect(mockModels.Flow.findAll).toHaveBeenCalledWith({
        where: { botId: 'bot-123' },
        order: [['createdAt', 'DESC']]
      });
      expect(result).toEqual(mockFlows);
    });

    it('should return empty array on error', async () => {
      mockModels.Flow.findAll.mockRejectedValue(new Error('Database error'));

      const result = await databaseService.getFlowsByBot('bot-123');

      expect(result).toEqual([]);
    });
  });

  describe('getIntentItemByName', () => {
    it('should return intent item when found', async () => {
      const mockIntentItem = { id: 'intent-123', name: 'greeting', botId: 'bot-123' };
      mockModels.IntentItems.findOne.mockResolvedValue(mockIntentItem);

      const result = await databaseService.getIntentItemByName('bot-123', 'greeting');

      expect(mockModels.IntentItems.findOne).toHaveBeenCalledWith({
        where: { botId: 'bot-123', name: 'greeting' }
      });
      expect(result).toEqual(mockIntentItem);
    });

    it('should return null when not found', async () => {
      mockModels.IntentItems.findOne.mockResolvedValue(null);

      const result = await databaseService.getIntentItemByName('bot-123', 'greeting');

      expect(result).toBeNull();
    });

    it('should return null on error', async () => {
      mockModels.IntentItems.findOne.mockRejectedValue(new Error('Database error'));

      const result = await databaseService.getIntentItemByName('bot-123', 'greeting');

      expect(result).toBeNull();
    });
  });

  describe('getIntentItemWithFlow', () => {
    it('should return intent item with flow', async () => {
      const mockIntentItem = { 
        id: 'intent-123', 
        flow: { id: 'flow-123' } as any
      };
      mockModels.IntentItems.findByPk.mockResolvedValue(mockIntentItem);

      const result = await databaseService.getIntentItemWithFlow('intent-123');

      expect(mockModels.IntentItems.findByPk).toHaveBeenCalledWith('intent-123', {
        include: [{ model: mockModels.Flow, as: 'flow' }]
      });
      expect(result).toEqual(mockIntentItem);
    });

    it('should return null on error', async () => {
      mockModels.IntentItems.findByPk.mockRejectedValue(new Error('Database error'));

      const result = await databaseService.getIntentItemWithFlow('intent-123');

      expect(result).toBeNull();
    });
  });

  describe('getFaqItemWithFlow', () => {
    it('should return FAQ item with flow', async () => {
      const mockFaqItem = { 
        id: 'faq-123', 
        flow: { id: 'flow-123' } as any
      };
      mockModels.FaqItems.findByPk.mockResolvedValue(mockFaqItem);

      const result = await databaseService.getFaqItemWithFlow('faq-123');

      expect(mockModels.FaqItems.findByPk).toHaveBeenCalledWith('faq-123', {
        include: [{ model: mockModels.Flow, as: 'flow' }]
      });
      expect(result).toEqual(mockFaqItem);
    });

    it('should return null on error', async () => {
      mockModels.FaqItems.findByPk.mockRejectedValue(new Error('Database error'));

      const result = await databaseService.getFaqItemWithFlow('faq-123');

      expect(result).toBeNull();
    });
  });

  describe('getFaqTranslation', () => {
    it('should return FAQ translation', async () => {
      const mockTranslation = { 
        id: 'translation-123',
        faqItem: { id: 'faq-123' } as any,
        language: { code: 'en' } as any
      };
      mockModels.FaqTranslation.findOne.mockResolvedValue(mockTranslation);

      const result = await databaseService.getFaqTranslation('faq-123', 'en');

      expect(mockModels.FaqTranslation.findOne).toHaveBeenCalledWith({
        where: { faqId: 'faq-123' },
        include: [
          { model: mockModels.FaqItems, as: 'faqItem' },
          { 
            model: mockModels.Language, 
            as: 'language',
            where: { code: 'en' }
          }
        ]
      });
      expect(result).toEqual(mockTranslation);
    });

    it('should return null on error', async () => {
      mockModels.FaqTranslation.findOne.mockRejectedValue(new Error('Database error'));

      const result = await databaseService.getFaqTranslation('faq-123', 'en');

      expect(result).toBeNull();
    });
  });

  describe('getEntityByIdAndBotId', () => {
    it('should return entity when found', async () => {
      const mockEntity = { id: 'entity-123', botId: 'bot-123' };
      mockModels.Entities.findOne.mockResolvedValue(mockEntity);

      const result = await databaseService.getEntityByIdAndBotId('bot-123', 'entity-123');

      expect(mockModels.Entities.findOne).toHaveBeenCalledWith({
        where: { botId: 'bot-123', id: 'entity-123' }
      });
      expect(result).toEqual(mockEntity);
    });

    it('should return null on error', async () => {
      mockModels.Entities.findOne.mockRejectedValue(new Error('Database error'));

      const result = await databaseService.getEntityByIdAndBotId('bot-123', 'entity-123');

      expect(result).toBeNull();
    });
  });

  describe('getBot', () => {
    it('should return bot when found', async () => {
      const mockBot = { 
        id: 'bot-123', 
        trainingJobs: [{ id: 'job-123', status: 'COMPLETED' }] as any
      };
      mockModels.Bot.findOne.mockResolvedValue(mockBot);

      const result = await databaseService.getBot('bot-123');

      expect(mockModels.Bot.findOne).toHaveBeenCalledWith({
        where: { 
          id: 'bot-123',
          status: { [Op.ne]: BotStatus.INACTIVE }
        },
        include: [{
          model: mockModels.TrainingJob,
          as: 'trainingJobs',
          where: { status: 'COMPLETED' },
          order: [['updatedAt', 'DESC']],
          limit: 1
        }]
      });
      expect(result).toEqual(mockBot);
    });

    it('should return bot with published model when isPublished is true', async () => {
      const mockBot = { 
        id: 'bot-123', 
        publishedModel: { id: 'model-123', status: 'COMPLETED' } as any
      };
      mockModels.Bot.findOne.mockResolvedValue(mockBot);

      const result = await databaseService.getBot('bot-123', true);

      expect(mockModels.Bot.findOne).toHaveBeenCalledWith({
        where: { 
          id: 'bot-123',
          status: { [Op.ne]: BotStatus.INACTIVE }
        },
        include: {
          model: mockModels.TrainingJob,
          as: 'publishedModel',
          where: { status: 'COMPLETED' }
        }
      });
      expect(result).toEqual(mockBot);
    });

    it('should return null when bot not found', async () => {
      mockModels.Bot.findOne.mockResolvedValue(null);

      const result = await databaseService.getBot('bot-123');

      expect(result).toBeNull();
    });

    it('should return null on error', async () => {
      mockModels.Bot.findOne.mockRejectedValue(new Error('Database error'));

      const result = await databaseService.getBot('bot-123');

      expect(result).toBeNull();
    });
  });
});