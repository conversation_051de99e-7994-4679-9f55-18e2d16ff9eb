/**
 * Debug test to identify the root cause of app initialization failure
 */

// Mock all external dependencies first
jest.mock("cors", () => jest.fn(() => jest.fn()));
jest.mock("helmet", () => jest.fn(() => jest.fn()));
jest.mock("compression", () => jest.fn(() => jest.fn()));
jest.mock("morgan", () => jest.fn(() => jest.fn()));
jest.mock("swagger-ui-express", () => ({
  serve: jest.fn(),
  setup: jest.fn(() => jest.fn()),
}));

// Mock config
jest.mock("../config", () => {
  const mockConfig = {
    server: {
      port: 3000,
      env: "test",
      corsOrigins: ["http://localhost:3000"],
    },
    database: {
      url: "postgresql://test:test@localhost:5432/test_db",
      host: "localhost",
      port: 5432,
      name: "test_db",
      user: "test",
      password: "test",
      ssl: false,
      maxConnections: 20,
    },
    redis: {
      url: "redis://localhost:6379",
      host: "localhost",
      port: 6379,
      db: 0,
      keyPrefix: "chatbot:",
    },
    services: {
      rasaNluUrl: "http://localhost:5005",
      botBuilderUrl: "http://localhost:3000",
      chatServiceUrl: "http://localhost:3002",
    },
    security: {
      jwtSecret: "test-secret",
      apiKey: "test-api-key",
    },
    session: {
      ttlMinutes: 30,
      maxConcurrentSessions: 10,
      scriptTimeoutMs: 100,
      asyncTimeoutMs: 30000,
    },
    logging: {
      level: "info",
    },
  };

  return {
    __esModule: true,
    default: mockConfig,
  };
});

jest.mock("../config/swagger", () => ({}));

// Mock @neuratalk modules
jest.mock("@neuratalk/bot-store", () => ({
  DatabaseConnection: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    models: {},
  })),
}));

jest.mock("@neuratalk/common", () => ({
  ApiResponse: { success: jest.fn(), error: jest.fn() },
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock all services
jest.mock("../services/redis.service", () => ({
  RedisService: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    get: jest.fn().mockResolvedValue(null),
    set: jest.fn().mockResolvedValue(undefined),
    getOrSet: jest.fn().mockImplementation(async (key, fetchFn) => await fetchFn()),
  })),
}));

jest.mock("../services/nlu.service", () => ({
  NLUService: jest.fn().mockImplementation(() => ({
    testConnection: jest.fn().mockResolvedValue(true),
  })),
}));

jest.mock("../services/database.service", () => ({
  DatabaseService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../services/application-repository.service", () => ({
  ApplicationRepository: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../services/debugger.service", () => ({
  DebuggerService: {
    getInstance: jest.fn().mockReturnValue({
      initialize: jest.fn(),
    }),
  },
}));

jest.mock("../services/socket.service", () => ({
  SocketService: jest.fn().mockImplementation(() => ({
    initialize: jest.fn(),
    disconnect: jest.fn().mockResolvedValue(undefined),
  })),
}));

// Mock routes
jest.mock("../routes/socket.routes", () => ({
  createSocketRoutes: jest.fn().mockReturnValue({}),
}));

jest.mock("../routes/index.router", () => ({
  createRoutes: jest.fn().mockReturnValue([]),
}));

// Mock express
const mockApp = {
  use: jest.fn(),
  get: jest.fn(),
  listen: jest.fn(),
};

jest.mock("express", () => {
  const mockExpress = jest.fn(() => mockApp) as any;
  mockExpress.json = jest.fn(() => jest.fn());
  mockExpress.urlencoded = jest.fn(() => jest.fn());
  return mockExpress;
});

// Mock http
const mockServer: any = {
  listen: jest.fn((port: number, callback?: () => void) => {
    if (callback) callback();
    return mockServer;
  }),
  close: jest.fn(),
};

jest.mock("http", () => ({
  createServer: jest.fn(() => mockServer),
}));

import { App } from "../app";

describe("App Debug Test", () => {
  let originalProcessExit: typeof process.exit;
  let originalConsoleError: typeof console.error;
  let capturedErrors: any[] = [];
  let exitCalled = false;
  let exitCode: number | undefined;

  beforeAll(() => {
    // Capture process.exit calls
    originalProcessExit = process.exit;
    process.exit = jest.fn().mockImplementation((code?: number) => {
      exitCalled = true;
      exitCode = code;
      throw new Error(`Process exit called with code: ${code}`);
    }) as any;

    // Capture console.error calls
    originalConsoleError = console.error;
    console.error = jest.fn().mockImplementation((...args: any[]) => {
      capturedErrors.push(args);
      originalConsoleError(...args);
    });
  });

  afterAll(() => {
    process.exit = originalProcessExit;
    console.error = originalConsoleError;
  });

  beforeEach(() => {
    capturedErrors = [];
    exitCalled = false;
    exitCode = undefined;
    jest.clearAllMocks();
  });

  it("should identify the root cause of initialization failure", async () => {
    console.log("=== Starting App Debug Test ===");

    let app: App;

    // Mock logger to capture all log calls
    const { logger } = require("@neuratalk/common");
    const loggerCalls: any[] = [];
    logger.error = jest.fn((...args: any[]) => {
      loggerCalls.push(["error", ...args]);
      console.log("LOGGER ERROR:", ...args);
    });
    logger.info = jest.fn((...args: any[]) => {
      loggerCalls.push(["info", ...args]);
      console.log("LOGGER INFO:", ...args);
    });
    logger.warn = jest.fn((...args: any[]) => {
      loggerCalls.push(["warn", ...args]);
      console.log("LOGGER WARN:", ...args);
    });

    try {
      console.log("1. Creating App instance...");
      app = new App();
      console.log("2. App instance created successfully");

      console.log("3. Starting app...");

      // Try to call each initialization method individually to isolate the issue
      try {
        console.log("3a. Calling initializeCoreServices...");
        await (app as any).initializeCoreServices();
        console.log("3b. initializeCoreServices completed");
      } catch (coreError: any) {
        console.log("ERROR in initializeCoreServices:", coreError.message);
        throw coreError;
      }

      try {
        console.log("3c. Calling initializeMiddleware...");
        (app as any).initializeMiddleware();
        console.log("3d. initializeMiddleware completed");
      } catch (middlewareError: any) {
        console.log("ERROR in initializeMiddleware:", middlewareError.message);
        throw middlewareError;
      }

      try {
        console.log("3e. Calling initializeRoutes...");
        (app as any).initializeRoutes();
        console.log("3f. initializeRoutes completed");
      } catch (routesError: any) {
        console.log("ERROR in initializeRoutes:", routesError.message);
        throw routesError;
      }

      try {
        console.log("3g. Calling initializeErrorHandling...");
        (app as any).initializeErrorHandling();
        console.log("3h. initializeErrorHandling completed");
      } catch (errorHandlingError: any) {
        console.log("ERROR in initializeErrorHandling:", errorHandlingError.message);
        throw errorHandlingError;
      }

      console.log("4. All initialization methods completed successfully");

      // Now try to start the server
      try {
        console.log("4a. Starting HTTP server...");
        const port = 3000;
        mockServer.listen(port, () => {
          console.log("4b. HTTP server started successfully");
        });
        console.log("4c. Server listen method called");
      } catch (serverError: any) {
        console.log("ERROR starting server:", serverError.message);
        throw serverError;
      }

      // If we get here, the app started successfully
      expect(mockServer.listen).toHaveBeenCalled();
    } catch (error: any) {
      console.log("=== ERROR CAUGHT ===");
      console.log("Error message:", error.message);
      console.log("Error stack:", error.stack);
      console.log("Exit called:", exitCalled);
      console.log("Exit code:", exitCode);
      console.log("Captured errors:", capturedErrors);
      console.log("Logger calls:", loggerCalls);
      console.log("===================");

      // Re-throw the error to fail the test with details
      throw new Error(
        `App initialization failed: ${error.message}. Exit called: ${exitCalled}, Exit code: ${exitCode}, Logger calls: ${JSON.stringify(loggerCalls)}`,
      );
    }
  });
});
