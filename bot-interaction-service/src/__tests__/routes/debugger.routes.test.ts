/**
 * Debugger Routes Tests
 */

import { Router } from "express";
import { Request, Response } from "express";
import { createDebuggerRoutes } from "../../routes/debugger.routes";
import { AppContext } from "../../types/context.types";

describe("createDebuggerRoutes", () => {
  let mockContext: AppContext;
  let mockDebuggerService: any;
  let router: Router;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    // Mock debugger service
    mockDebuggerService = {
      addClient: jest.fn(),
      removeClient: jest.fn(),
    };

    // Mock context
    mockContext = {
      debuggerService: mockDebuggerService,
      redis: {
        connect: jest.fn(),
        disconnect: jest.fn(),
        get: jest.fn(),
        set: jest.fn(),
        del: jest.fn(),
        exists: jest.fn(),
        expire: jest.fn(),
        getOrSet: jest.fn(),
      },
      db: {
        connect: jest.fn(),
        disconnect: jest.fn(),
      },
      nlu: {
        testConnection: jest.fn(),
        parseMessage: jest.fn(),
      },
      databaseService: {
        getFlow: jest.fn(),
        getFlowsByBot: jest.fn(),
        getBot: jest.fn(),
        getIntentItemByName: jest.fn(),
        getFaqItemByName: jest.fn(),
        getFaqTranslation: jest.fn(),
        getEntityByIdAndBotId: jest.fn(),
      },
      applicationRepository: {
        getFlow: jest.fn(),
        getFlowsByBot: jest.fn(),
        getBot: jest.fn(),
        getIntentItemByName: jest.fn(),
        getFaqItemByName: jest.fn(),
        getFaqTranslation: jest.fn(),
        getEntityByIdAndBotId: jest.fn(),
      },
      nluService: {
        testConnection: jest.fn(),
        parseMessage: jest.fn(),
      },
      socketService: {
        initialize: jest.fn(),
        disconnect: jest.fn(),
        emit: jest.fn(),
        on: jest.fn(),
        off: jest.fn(),
      },
    } as any;

    // Mock request and response objects
    mockReq = {
      params: { conversationId: "test-conversation-id" },
      on: jest.fn(),
    };

    mockRes = {
      writeHead: jest.fn(),
      write: jest.fn(),
      end: jest.fn(),
    };

    router = createDebuggerRoutes(mockContext);
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe("router creation", () => {
    it("should create a router instance", () => {
      expect(router).toBeDefined();
      expect(typeof router).toBe("function");
    });

    it("should extract debuggerService from context", () => {
      expect(mockContext.debuggerService).toBe(mockDebuggerService);
    });
  });

  describe("route registration", () => {
    let mockRouter: jest.Mocked<Router>;

    beforeEach(() => {
      mockRouter = {
        get: jest.fn(),
        post: jest.fn(),
        put: jest.fn(),
        delete: jest.fn(),
        patch: jest.fn(),
        use: jest.fn(),
      } as any;

      jest.spyOn(require("express"), "Router").mockReturnValue(mockRouter);
      createDebuggerRoutes(mockContext);
    });

    it("should register GET /debugger/stream/:conversationId route", () => {
      expect(mockRouter.get).toHaveBeenCalledWith(
        "/debugger/stream/:conversationId",
        expect.any(Function),
      );
    });

    it("should register exactly 1 route", () => {
      expect(mockRouter.get).toHaveBeenCalledTimes(1);
    });
  });

  describe("SSE endpoint handler", () => {
    let routeHandler: Function;

    beforeEach(() => {
      const mockRouter = {
        get: jest.fn(),
      } as any;

      jest.spyOn(require("express"), "Router").mockReturnValue(mockRouter);
      createDebuggerRoutes(mockContext);

      // Extract the route handler
      routeHandler = mockRouter.get.mock.calls[0][1];
    });

    it("should set correct SSE headers", () => {
      routeHandler(mockReq, mockRes);

      expect(mockRes.writeHead).toHaveBeenCalledWith(200, {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
      });
    });

    it("should add client to debugger service", () => {
      routeHandler(mockReq, mockRes);

      expect(mockDebuggerService.addClient).toHaveBeenCalledWith(
        "test-conversation-id",
        mockRes,
        mockReq,
      );
    });

    it("should set up heartbeat interval", () => {
      const setIntervalSpy = jest.spyOn(global, "setInterval");

      routeHandler(mockReq, mockRes);

      expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 15000);
    });

    it("should send heartbeat messages", () => {
      routeHandler(mockReq, mockRes);

      // Fast-forward time to trigger heartbeat
      jest.advanceTimersByTime(15000);

      expect(mockRes.write).toHaveBeenCalledWith("event: heartbeat\ndata: {}\n\n");
    });

    it("should set up close event listener", () => {
      routeHandler(mockReq, mockRes);

      expect(mockReq.on).toHaveBeenCalledWith("close", expect.any(Function));
    });

    describe("connection close handling", () => {
      it("should clear heartbeat interval on close", () => {
        const clearIntervalSpy = jest.spyOn(global, "clearInterval");
        let closeHandler: Function | undefined;

        (mockReq.on as jest.Mock).mockImplementation((event, handler) => {
          if (event === "close") {
            closeHandler = handler;
          }
        });

        routeHandler(mockReq, mockRes);

        // Simulate connection close
        if (closeHandler) {
          closeHandler();
        }

        expect(clearIntervalSpy).toHaveBeenCalled();
      });

      it("should remove client from debugger service on close", () => {
        let closeHandler: Function | undefined;

        (mockReq.on as jest.Mock).mockImplementation((event, handler) => {
          if (event === "close") {
            closeHandler = handler;
          }
        });

        routeHandler(mockReq, mockRes);

        // Simulate connection close
        if (closeHandler) {
          closeHandler();
        }

        expect(mockDebuggerService.removeClient).toHaveBeenCalledWith("test-conversation-id");
      });

      it("should end response on close", () => {
        let closeHandler: Function | undefined;

        (mockReq.on as jest.Mock).mockImplementation((event, handler) => {
          if (event === "close") {
            closeHandler = handler;
          }
        });

        routeHandler(mockReq, mockRes);

        // Simulate connection close
        if (closeHandler) {
          closeHandler();
        }

        expect(mockRes.end).toHaveBeenCalled();
      });
    });
  });

  describe("route parameters", () => {
    it("should extract conversationId from request params", () => {
      const mockRouter = {
        get: jest.fn(),
      } as any;

      jest.spyOn(require("express"), "Router").mockReturnValue(mockRouter);
      createDebuggerRoutes(mockContext);

      const routeHandler = mockRouter.get.mock.calls[0][1];
      routeHandler(mockReq, mockRes);

      expect(mockDebuggerService.addClient).toHaveBeenCalledWith(
        "test-conversation-id",
        expect.anything(),
        expect.anything(),
      );
    });

    it("should handle different conversation IDs", () => {
      const mockRouter = {
        get: jest.fn(),
      } as any;

      jest.spyOn(require("express"), "Router").mockReturnValue(mockRouter);
      createDebuggerRoutes(mockContext);

      const routeHandler = mockRouter.get.mock.calls[0][1];

      // Test with different conversation ID
      const differentReq = {
        ...mockReq,
        params: { conversationId: "different-conversation-id" },
      };

      routeHandler(differentReq, mockRes);

      expect(mockDebuggerService.addClient).toHaveBeenCalledWith(
        "different-conversation-id",
        expect.anything(),
        expect.anything(),
      );
    });
  });

  describe("error handling", () => {
    it("should handle context parameter correctly", () => {
      expect(() => createDebuggerRoutes(mockContext)).not.toThrow();
    });

    it("should handle missing debuggerService", () => {
      const contextWithoutDebugger = { ...mockContext, debuggerService: undefined };
      expect(() => createDebuggerRoutes(contextWithoutDebugger as any)).not.toThrow();
    });

    it("should handle null context", () => {
      expect(() => createDebuggerRoutes(null as any)).toThrow(
        "Cannot destructure property 'debuggerService'",
      );
    });
  });

  describe("return value", () => {
    it("should return a Router instance", () => {
      // Create a fresh router mock for this test
      const mockRouterInstance = {
        get: jest.fn(),
        post: jest.fn(),
        put: jest.fn(),
        delete: jest.fn(),
        patch: jest.fn(),
        use: jest.fn(),
      };

      jest.spyOn(require("express"), "Router").mockReturnValue(mockRouterInstance);

      const result = createDebuggerRoutes(mockContext);
      expect(result).toBeDefined();
      expect(typeof result).toBe("object");
      expect(result).toHaveProperty("use");
      expect(result).toHaveProperty("get");
      expect(result).toHaveProperty("post");
      expect(result).toHaveProperty("delete");
    });
  });

  describe("heartbeat functionality", () => {
    it("should send multiple heartbeats over time", () => {
      const mockRouter = {
        get: jest.fn(),
      } as any;

      jest.spyOn(require("express"), "Router").mockReturnValue(mockRouter);
      createDebuggerRoutes(mockContext);

      const routeHandler = mockRouter.get.mock.calls[0][1];
      routeHandler(mockReq, mockRes);

      // Fast-forward time to trigger multiple heartbeats
      jest.advanceTimersByTime(45000); // 3 heartbeats

      expect(mockRes.write).toHaveBeenCalledTimes(3);
      expect(mockRes.write).toHaveBeenCalledWith("event: heartbeat\ndata: {}\n\n");
    });
  });
});
