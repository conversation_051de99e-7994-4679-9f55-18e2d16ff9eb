/**
 * Health Routes Tests
 */

import { Router } from "express";
import { createHealthRoutes } from "../../routes/health.routes";
import { HealthController } from "../../controllers/health.controller";
import { AppContext } from "../../types/context.types";

// Mock the controller
jest.mock("../../controllers/health.controller");

describe("createHealthRoutes", () => {
  let mockContext: AppContext;
  let mockController: jest.Mocked<HealthController>;
  let router: Router;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock context
    mockContext = {
      redis: {
        connect: jest.fn(),
        disconnect: jest.fn(),
        get: jest.fn(),
        set: jest.fn(),
        del: jest.fn(),
        exists: jest.fn(),
        expire: jest.fn(),
        getOrSet: jest.fn(),
      },
      db: {
        connect: jest.fn(),
        disconnect: jest.fn(),
      },
      nlu: {
        testConnection: jest.fn(),
        parseMessage: jest.fn(),
      },
      databaseService: {
        getFlow: jest.fn(),
        getFlowsByBot: jest.fn(),
        getBot: jest.fn(),
        getIntentItemByName: jest.fn(),
        getFaqItemByName: jest.fn(),
        getFaqTranslation: jest.fn(),
        getEntityByIdAndBotId: jest.fn(),
      },
      applicationRepository: {
        getFlow: jest.fn(),
        getFlowsByBot: jest.fn(),
        getBot: jest.fn(),
        getIntentItemByName: jest.fn(),
        getFaqItemByName: jest.fn(),
        getFaqTranslation: jest.fn(),
        getEntityByIdAndBotId: jest.fn(),
      },
      nluService: {
        testConnection: jest.fn(),
        parseMessage: jest.fn(),
      },
      socketService: {
        initialize: jest.fn(),
        disconnect: jest.fn(),
        emit: jest.fn(),
        on: jest.fn(),
        off: jest.fn(),
      },
    } as any;

    // Mock controller methods
    mockController = {
      healthCheck: jest.fn(),
      readinessCheck: jest.fn(),
      livenessCheck: jest.fn(),
      metrics: jest.fn(),
    } as any;

    (HealthController as jest.MockedClass<typeof HealthController>).mockImplementation(
      () => mockController,
    );

    router = createHealthRoutes(mockContext);
  });

  describe("router creation", () => {
    it("should create a router instance", () => {
      expect(router).toBeDefined();
      expect(typeof router).toBe("function");
    });

    it("should create HealthController with correct dependencies", () => {
      expect(HealthController).toHaveBeenCalledWith(
        mockContext.redis,
        mockContext.db,
        mockContext.nlu,
      );
    });
  });

  describe("route registration", () => {
    let mockRouter: jest.Mocked<Router>;

    beforeEach(() => {
      mockRouter = {
        get: jest.fn(),
        post: jest.fn(),
        put: jest.fn(),
        delete: jest.fn(),
        patch: jest.fn(),
        use: jest.fn(),
      } as any;

      jest.spyOn(require("express"), "Router").mockReturnValue(mockRouter);
      createHealthRoutes(mockContext);
    });

    it("should register GET / route for health check", () => {
      expect(mockRouter.get).toHaveBeenCalledWith("/", mockController.healthCheck);
    });

    it("should register GET /ready route for readiness check", () => {
      expect(mockRouter.get).toHaveBeenCalledWith("/ready", mockController.readinessCheck);
    });

    it("should register GET /live route for liveness check", () => {
      expect(mockRouter.get).toHaveBeenCalledWith("/live", mockController.livenessCheck);
    });

    it("should register GET /metrics route for metrics", () => {
      expect(mockRouter.get).toHaveBeenCalledWith("/metrics", mockController.metrics);
    });

    it("should register exactly 4 GET routes", () => {
      expect(mockRouter.get).toHaveBeenCalledTimes(4);
    });
  });

  describe("route paths", () => {
    it("should have correct route paths", () => {
      const routePaths = ["/", "/ready", "/live", "/metrics"];

      // Verify that these are the expected paths
      expect(routePaths).toHaveLength(4);
      expect(routePaths).toContain("/");
      expect(routePaths).toContain("/ready");
      expect(routePaths).toContain("/live");
      expect(routePaths).toContain("/metrics");
    });
  });

  describe("controller method binding", () => {
    it("should bind healthCheck method correctly", () => {
      expect(mockController.healthCheck).toBeDefined();
      expect(typeof mockController.healthCheck).toBe("function");
    });

    it("should bind readinessCheck method correctly", () => {
      expect(mockController.readinessCheck).toBeDefined();
      expect(typeof mockController.readinessCheck).toBe("function");
    });

    it("should bind livenessCheck method correctly", () => {
      expect(mockController.livenessCheck).toBeDefined();
      expect(typeof mockController.livenessCheck).toBe("function");
    });

    it("should bind metrics method correctly", () => {
      expect(mockController.metrics).toBeDefined();
      expect(typeof mockController.metrics).toBe("function");
    });
  });

  describe("context dependencies", () => {
    it("should pass redis service to controller", () => {
      expect(HealthController).toHaveBeenCalledWith(
        expect.objectContaining({
          connect: expect.any(Function),
          disconnect: expect.any(Function),
          get: expect.any(Function),
          set: expect.any(Function),
        }),
        expect.anything(),
        expect.anything(),
      );
    });

    it("should pass database service to controller", () => {
      expect(HealthController).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          connect: expect.any(Function),
          disconnect: expect.any(Function),
        }),
        expect.anything(),
      );
    });

    it("should pass NLU service to controller", () => {
      expect(HealthController).toHaveBeenCalledWith(
        expect.anything(),
        expect.anything(),
        expect.objectContaining({
          testConnection: expect.any(Function),
          parseMessage: expect.any(Function),
        }),
      );
    });
  });

  describe("error handling", () => {
    it("should handle context parameter correctly", () => {
      expect(() => createHealthRoutes(mockContext)).not.toThrow();
    });

    it("should handle missing redis service", () => {
      const contextWithoutRedis = { ...mockContext, redis: undefined };
      expect(() => createHealthRoutes(contextWithoutRedis as any)).not.toThrow();
    });

    it("should handle missing db service", () => {
      const contextWithoutDb = { ...mockContext, db: undefined };
      expect(() => createHealthRoutes(contextWithoutDb as any)).not.toThrow();
    });

    it("should handle missing nlu service", () => {
      const contextWithoutNlu = { ...mockContext, nlu: undefined };
      expect(() => createHealthRoutes(contextWithoutNlu as any)).not.toThrow();
    });
  });

  describe("return value", () => {
    it("should return a Router instance", () => {
      const result = createHealthRoutes(mockContext);
      expect(result).toBeDefined();
      expect(typeof result).toBe("object");
      // Router is an object with router methods
      expect(result).toHaveProperty("use");
      expect(result).toHaveProperty("get");
      expect(result).toHaveProperty("post");
      expect(result).toHaveProperty("delete");
    });
  });

  describe("route methods", () => {
    it("should only use GET methods for all health routes", () => {
      const mockRouter = {
        get: jest.fn(),
        post: jest.fn(),
        put: jest.fn(),
        delete: jest.fn(),
        patch: jest.fn(),
        use: jest.fn(),
      } as any;

      jest.spyOn(require("express"), "Router").mockReturnValue(mockRouter);
      createHealthRoutes(mockContext);

      expect(mockRouter.get).toHaveBeenCalledTimes(4);
      expect(mockRouter.post).not.toHaveBeenCalled();
      expect(mockRouter.put).not.toHaveBeenCalled();
      expect(mockRouter.delete).not.toHaveBeenCalled();
      expect(mockRouter.patch).not.toHaveBeenCalled();
    });
  });
});
