/**
 * Socket Routes Tests
 */

import { Socket } from "socket.io";
import { createSocketRoutes } from "../../routes/socket.routes";
import { ConversationSocketController } from "../../controllers/conversation.socket.controller";
import { DebuggerSocketController } from "../../controllers/debugger.socket.controller";
import { SocketEvents } from "../../constants/socket.events";
import { validateSocketPayloadHOC } from "../../utils/socket-validator";
import { AppContext } from "../../types/context.types";

// Mock controllers
jest.mock("../../controllers/conversation.socket.controller");
jest.mock("../../controllers/debugger.socket.controller");

// Mock socket validator
jest.mock("../../utils/socket-validator");

// Mock schemas
jest.mock("../../schemas/conversation.schemas", () => ({
  ConversationIdPayloadSchema: {},
  SocketMessagePayloadSchema: {},
}));

describe("createSocketRoutes", () => {
  let mockContext: AppContext;
  let mockSocket: jest.Mocked<Socket>;
  let mockSocketService: any;
  let mockConversationController: jest.Mocked<ConversationSocketController>;
  let mockDebuggerController: jest.Mocked<DebuggerSocketController>;
  let mockValidateHOC: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock socket
    mockSocket = {
      on: jest.fn(),
      emit: jest.fn(),
      join: jest.fn(),
      leave: jest.fn(),
      id: "test-socket-id",
    } as any;

    // Mock socket service with io
    mockSocketService = {
      io: {
        on: jest.fn(),
        emit: jest.fn(),
      },
      initialize: jest.fn(),
      disconnect: jest.fn(),
    };

    // Mock context
    mockContext = {
      socketService: mockSocketService,
      redis: {
        connect: jest.fn(),
        disconnect: jest.fn(),
        get: jest.fn(),
        set: jest.fn(),
        del: jest.fn(),
        exists: jest.fn(),
        expire: jest.fn(),
        getOrSet: jest.fn(),
      },
      db: {
        connect: jest.fn(),
        disconnect: jest.fn(),
      },
      nlu: {
        testConnection: jest.fn(),
        parseMessage: jest.fn(),
      },
      databaseService: {
        getFlow: jest.fn(),
        getFlowsByBot: jest.fn(),
        getBot: jest.fn(),
        getIntentItemByName: jest.fn(),
        getFaqItemByName: jest.fn(),
        getFaqTranslation: jest.fn(),
        getEntityByIdAndBotId: jest.fn(),
      },
      applicationRepository: {
        getFlow: jest.fn(),
        getFlowsByBot: jest.fn(),
        getBot: jest.fn(),
        getIntentItemByName: jest.fn(),
        getFaqItemByName: jest.fn(),
        getFaqTranslation: jest.fn(),
        getEntityByIdAndBotId: jest.fn(),
      },
      nluService: {
        testConnection: jest.fn(),
        parseMessage: jest.fn(),
      },
    } as any;

    // Mock controllers
    mockConversationController = {
      handleJoinConversation: jest.fn(),
      handleSendMessage: jest.fn(),
      handleSendPreviewMessage: jest.fn(),
      handleDeleteConversationCache: jest.fn(),
    } as any;

    mockDebuggerController = {
      handleDebuggerSubscribe: jest.fn(),
      handleDebuggerUnsubscribe: jest.fn(),
    } as any;

    (
      ConversationSocketController as jest.MockedClass<typeof ConversationSocketController>
    ).mockImplementation(() => mockConversationController);

    (
      DebuggerSocketController as jest.MockedClass<typeof DebuggerSocketController>
    ).mockImplementation(() => mockDebuggerController);

    // Mock validation HOC
    mockValidateHOC = jest.fn().mockImplementation((schema, socket, event) => {
      return (handler: Function) => handler;
    });
    (validateSocketPayloadHOC as jest.Mock).mockImplementation(mockValidateHOC);
  });

  describe("socket routes setup", () => {
    it("should create controller instances with context", () => {
      createSocketRoutes(mockContext);

      expect(ConversationSocketController).toHaveBeenCalledWith(mockContext);
      expect(DebuggerSocketController).toHaveBeenCalledWith(mockContext);
    });

    it("should set up connection event listener", () => {
      createSocketRoutes(mockContext);

      expect(mockSocketService.io.on).toHaveBeenCalledWith(
        SocketEvents.CONNECTION,
        expect.any(Function),
      );
    });

    it("should extract socketService from context", () => {
      createSocketRoutes(mockContext);

      expect(mockSocketService.io.on).toHaveBeenCalled();
    });
  });

  describe("socket event registration", () => {
    let connectionHandler: Function;

    beforeEach(() => {
      createSocketRoutes(mockContext);
      connectionHandler = mockSocketService.io.on.mock.calls[0][1];
    });

    it("should register JOIN_CONVERSATION event", () => {
      connectionHandler(mockSocket);

      expect(mockSocket.on).toHaveBeenCalledWith(
        SocketEvents.JOIN_CONVERSATION,
        expect.any(Function),
      );
    });

    it("should register SEND_MESSAGE event", () => {
      connectionHandler(mockSocket);

      expect(mockSocket.on).toHaveBeenCalledWith(SocketEvents.SEND_MESSAGE, expect.any(Function));
    });

    it("should register SEND_PREVIEW_MESSAGE event", () => {
      connectionHandler(mockSocket);

      expect(mockSocket.on).toHaveBeenCalledWith(
        SocketEvents.SEND_PREVIEW_MESSAGE,
        expect.any(Function),
      );
    });

    it("should register DELETE_CONVERSATION_CACHE event", () => {
      connectionHandler(mockSocket);

      expect(mockSocket.on).toHaveBeenCalledWith(
        SocketEvents.DELETE_CONVERSATION_CACHE,
        expect.any(Function),
      );
    });

    it("should register DEBUGGER_SUBSCRIBE event", () => {
      connectionHandler(mockSocket);

      expect(mockSocket.on).toHaveBeenCalledWith(
        SocketEvents.DEBUGGER_SUBSCRIBE,
        expect.any(Function),
      );
    });

    it("should register DEBUGGER_UNSUBSCRIBE event", () => {
      connectionHandler(mockSocket);

      expect(mockSocket.on).toHaveBeenCalledWith(
        SocketEvents.DEBUGGER_UNSUBSCRIBE,
        expect.any(Function),
      );
    });

    it("should register exactly 6 socket events", () => {
      connectionHandler(mockSocket);

      expect(mockSocket.on).toHaveBeenCalledTimes(6);
    });
  });

  describe("validation middleware usage", () => {
    let connectionHandler: Function;

    beforeEach(() => {
      createSocketRoutes(mockContext);
      connectionHandler = mockSocketService.io.on.mock.calls[0][1];
      connectionHandler(mockSocket);
    });

    it("should use ConversationIdPayloadSchema for JOIN_CONVERSATION", () => {
      const { ConversationIdPayloadSchema } = require("../../schemas/conversation.schemas");

      expect(mockValidateHOC).toHaveBeenCalledWith(
        ConversationIdPayloadSchema,
        mockSocket,
        SocketEvents.JOIN_CONVERSATION,
      );
    });

    it("should use SocketMessagePayloadSchema for SEND_MESSAGE", () => {
      const { SocketMessagePayloadSchema } = require("../../schemas/conversation.schemas");

      expect(mockValidateHOC).toHaveBeenCalledWith(
        SocketMessagePayloadSchema,
        mockSocket,
        SocketEvents.SEND_MESSAGE,
      );
    });

    it("should use SocketMessagePayloadSchema for SEND_PREVIEW_MESSAGE", () => {
      const { SocketMessagePayloadSchema } = require("../../schemas/conversation.schemas");

      expect(mockValidateHOC).toHaveBeenCalledWith(
        SocketMessagePayloadSchema,
        mockSocket,
        SocketEvents.SEND_PREVIEW_MESSAGE,
      );
    });

    it("should use ConversationIdPayloadSchema for DELETE_CONVERSATION_CACHE", () => {
      const { ConversationIdPayloadSchema } = require("../../schemas/conversation.schemas");

      expect(mockValidateHOC).toHaveBeenCalledWith(
        ConversationIdPayloadSchema,
        mockSocket,
        SocketEvents.DELETE_CONVERSATION_CACHE,
      );
    });

    it("should use ConversationIdPayloadSchema for debugger events", () => {
      const { ConversationIdPayloadSchema } = require("../../schemas/conversation.schemas");

      expect(mockValidateHOC).toHaveBeenCalledWith(
        ConversationIdPayloadSchema,
        mockSocket,
        SocketEvents.DEBUGGER_SUBSCRIBE,
      );

      expect(mockValidateHOC).toHaveBeenCalledWith(
        ConversationIdPayloadSchema,
        mockSocket,
        SocketEvents.DEBUGGER_UNSUBSCRIBE,
      );
    });
  });

  describe("controller method binding", () => {
    let connectionHandler: Function;

    beforeEach(() => {
      // Mock the validation HOC to return the handler directly
      mockValidateHOC.mockImplementation((schema, socket, event) => {
        return (handler: Function) => {
          // Simulate calling the handler with mock data
          if (event === SocketEvents.JOIN_CONVERSATION) {
            return () => handler({ conversationId: "test-id" });
          } else if (
            event === SocketEvents.SEND_MESSAGE ||
            event === SocketEvents.SEND_PREVIEW_MESSAGE
          ) {
            return () => handler({ conversationId: "test-id", message: "test message" });
          } else if (event === SocketEvents.DELETE_CONVERSATION_CACHE) {
            return () => handler({ conversationId: "test-id" });
          } else if (
            event === SocketEvents.DEBUGGER_SUBSCRIBE ||
            event === SocketEvents.DEBUGGER_UNSUBSCRIBE
          ) {
            return () => handler({ conversationId: "test-id" });
          }
          return handler;
        };
      });

      createSocketRoutes(mockContext);
      connectionHandler = mockSocketService.io.on.mock.calls[0][1];
    });

    it("should bind handleJoinConversation correctly", () => {
      connectionHandler(mockSocket);

      // Get the handler for JOIN_CONVERSATION and call it
      const joinCall = mockSocket.on.mock.calls.find(
        (call) => call[0] === SocketEvents.JOIN_CONVERSATION,
      );
      const joinHandler = joinCall?.[1];

      if (joinHandler) {
        joinHandler();
      }

      expect(mockConversationController.handleJoinConversation).toHaveBeenCalledWith(
        mockSocket,
        "test-id",
      );
    });

    it("should bind handleSendMessage correctly", () => {
      connectionHandler(mockSocket);

      const sendCall = mockSocket.on.mock.calls.find(
        (call) => call[0] === SocketEvents.SEND_MESSAGE,
      );
      const sendHandler = sendCall?.[1];

      if (sendHandler) {
        sendHandler();
      }

      expect(mockConversationController.handleSendMessage).toHaveBeenCalledWith(mockSocket, {
        conversationId: "test-id",
        message: "test message",
      });
    });

    it("should bind handleSendPreviewMessage correctly", () => {
      connectionHandler(mockSocket);

      const previewCall = mockSocket.on.mock.calls.find(
        (call) => call[0] === SocketEvents.SEND_PREVIEW_MESSAGE,
      );
      const previewHandler = previewCall?.[1];

      if (previewHandler) {
        previewHandler();
      }

      expect(mockConversationController.handleSendPreviewMessage).toHaveBeenCalledWith(mockSocket, {
        conversationId: "test-id",
        message: "test message",
      });
    });
  });

  describe("error handling", () => {
    it("should handle context parameter correctly", () => {
      expect(() => createSocketRoutes(mockContext)).not.toThrow();
    });

    it("should handle missing socketService", () => {
      const contextWithoutSocket = { ...mockContext, socketService: undefined };
      expect(() => createSocketRoutes(contextWithoutSocket as any)).toThrow();
    });

    it("should handle null context", () => {
      expect(() => createSocketRoutes(null as any)).toThrow();
    });
  });

  describe("socket events constants", () => {
    it("should use correct socket event constants", () => {
      createSocketRoutes(mockContext);
      const connectionHandler = mockSocketService.io.on.mock.calls[0][1];
      connectionHandler(mockSocket);

      const registeredEvents = mockSocket.on.mock.calls.map((call) => call[0]);

      expect(registeredEvents).toContain(SocketEvents.JOIN_CONVERSATION);
      expect(registeredEvents).toContain(SocketEvents.SEND_MESSAGE);
      expect(registeredEvents).toContain(SocketEvents.SEND_PREVIEW_MESSAGE);
      expect(registeredEvents).toContain(SocketEvents.DELETE_CONVERSATION_CACHE);
      expect(registeredEvents).toContain(SocketEvents.DEBUGGER_SUBSCRIBE);
      expect(registeredEvents).toContain(SocketEvents.DEBUGGER_UNSUBSCRIBE);
    });
  });
});
