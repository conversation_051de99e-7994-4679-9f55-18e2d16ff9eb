/**
 * Index Router Tests
 */

import { Router } from "express";
import { createRoutes } from "../../routes/index.router";
import { createConversationRoutes } from "../../routes/conversation.routes";
import { createHealthRoutes } from "../../routes/health.routes";
import { createDebuggerRoutes } from "../../routes/debugger.routes";
import { AppContext } from "../../types/context.types";

// Mock the route creation functions
jest.mock("../../routes/conversation.routes");
jest.mock("../../routes/health.routes");
jest.mock("../../routes/debugger.routes");

describe("createRoutes", () => {
  let mockContext: AppContext;
  let mockConversationRouter: Router;
  let mockHealthRouter: Router;
  let mockDebuggerRouter: Router;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock context
    mockContext = {
      redis: {
        connect: jest.fn(),
        disconnect: jest.fn(),
        get: jest.fn(),
        set: jest.fn(),
        del: jest.fn(),
        exists: jest.fn(),
        expire: jest.fn(),
        getOrSet: jest.fn(),
      },
      db: {
        connect: jest.fn(),
        disconnect: jest.fn(),
      },
      nlu: {
        testConnection: jest.fn(),
        parseMessage: jest.fn(),
      },
      databaseService: {
        getFlow: jest.fn(),
        getFlowsByBot: jest.fn(),
        getBot: jest.fn(),
        getIntentItemByName: jest.fn(),
        getFaqItemByName: jest.fn(),
        getFaqTranslation: jest.fn(),
        getEntityByIdAndBotId: jest.fn(),
      },
      applicationRepository: {
        getFlow: jest.fn(),
        getFlowsByBot: jest.fn(),
        getBot: jest.fn(),
        getIntentItemByName: jest.fn(),
        getFaqItemByName: jest.fn(),
        getFaqTranslation: jest.fn(),
        getEntityByIdAndBotId: jest.fn(),
      },
      nluService: {
        testConnection: jest.fn(),
        parseMessage: jest.fn(),
      },
      socketService: {
        initialize: jest.fn(),
        disconnect: jest.fn(),
        emit: jest.fn(),
        on: jest.fn(),
        off: jest.fn(),
      },
    } as any;

    // Mock router instances
    mockConversationRouter = {
      use: jest.fn(),
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      patch: jest.fn(),
    } as any;

    mockHealthRouter = {
      use: jest.fn(),
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      patch: jest.fn(),
    } as any;

    mockDebuggerRouter = {
      use: jest.fn(),
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      patch: jest.fn(),
    } as any;

    // Mock the route creation functions
    (createConversationRoutes as jest.Mock).mockReturnValue(mockConversationRouter);
    (createHealthRoutes as jest.Mock).mockReturnValue(mockHealthRouter);
    (createDebuggerRoutes as jest.Mock).mockReturnValue(mockDebuggerRouter);
  });

  describe("route creation", () => {
    it("should create all route modules", () => {
      createRoutes(mockContext);

      expect(createConversationRoutes).toHaveBeenCalledWith(mockContext);
      expect(createHealthRoutes).toHaveBeenCalledWith(mockContext);
      expect(createDebuggerRoutes).toHaveBeenCalledWith(mockContext);
    });

    it("should call each route creation function exactly once", () => {
      createRoutes(mockContext);

      expect(createConversationRoutes).toHaveBeenCalledTimes(1);
      expect(createHealthRoutes).toHaveBeenCalledTimes(1);
      expect(createDebuggerRoutes).toHaveBeenCalledTimes(1);
    });

    it("should pass the same context to all route creation functions", () => {
      createRoutes(mockContext);

      expect(createConversationRoutes).toHaveBeenCalledWith(mockContext);
      expect(createHealthRoutes).toHaveBeenCalledWith(mockContext);
      expect(createDebuggerRoutes).toHaveBeenCalledWith(mockContext);
    });
  });

  describe("return value", () => {
    it("should return an array of routers", () => {
      const result = createRoutes(mockContext);

      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(3);
    });

    it("should return routers in correct order", () => {
      const result = createRoutes(mockContext);

      expect(result[0]).toBe(mockConversationRouter);
      expect(result[1]).toBe(mockHealthRouter);
      expect(result[2]).toBe(mockDebuggerRouter);
    });

    it("should return router instances", () => {
      const result = createRoutes(mockContext);

      result.forEach(router => {
        expect(router).toBeDefined();
        expect(typeof router).toBe("object");
        expect(router).toHaveProperty("use");
        expect(router).toHaveProperty("get");
        expect(router).toHaveProperty("post");
      });
    });
  });

  describe("context handling", () => {
    it("should handle valid context", () => {
      expect(() => createRoutes(mockContext)).not.toThrow();
    });

    it("should handle null context", () => {
      expect(() => createRoutes(null as any)).not.toThrow();
    });

    it("should handle undefined context", () => {
      expect(() => createRoutes(undefined as any)).not.toThrow();
    });

    it("should handle empty context", () => {
      expect(() => createRoutes({} as any)).not.toThrow();
    });
  });

  describe("route module dependencies", () => {
    it("should import conversation routes correctly", () => {
      createRoutes(mockContext);
      expect(createConversationRoutes).toHaveBeenCalled();
    });

    it("should import health routes correctly", () => {
      createRoutes(mockContext);
      expect(createHealthRoutes).toHaveBeenCalled();
    });

    it("should import debugger routes correctly", () => {
      createRoutes(mockContext);
      expect(createDebuggerRoutes).toHaveBeenCalled();
    });
  });

  describe("error handling", () => {
    it("should handle errors from conversation routes creation", () => {
      (createConversationRoutes as jest.Mock).mockImplementation(() => {
        throw new Error("Conversation routes error");
      });

      expect(() => createRoutes(mockContext)).toThrow("Conversation routes error");
    });

    it("should handle errors from health routes creation", () => {
      (createHealthRoutes as jest.Mock).mockImplementation(() => {
        throw new Error("Health routes error");
      });

      expect(() => createRoutes(mockContext)).toThrow("Health routes error");
    });

    it("should handle errors from debugger routes creation", () => {
      (createDebuggerRoutes as jest.Mock).mockImplementation(() => {
        throw new Error("Debugger routes error");
      });

      expect(() => createRoutes(mockContext)).toThrow("Debugger routes error");
    });
  });

  describe("array structure", () => {
    it("should return exactly 3 routers", () => {
      const result = createRoutes(mockContext);
      expect(result).toHaveLength(3);
    });

    it("should not return empty array", () => {
      const result = createRoutes(mockContext);
      expect(result.length).toBeGreaterThan(0);
    });

    it("should not contain null or undefined routers", () => {
      const result = createRoutes(mockContext);
      result.forEach(router => {
        expect(router).not.toBeNull();
        expect(router).not.toBeUndefined();
      });
    });
  });

  describe("function signature", () => {
    it("should accept AppContext parameter", () => {
      expect(() => createRoutes(mockContext)).not.toThrow();
    });

    it("should return Router array", () => {
      const result = createRoutes(mockContext);
      expect(Array.isArray(result)).toBe(true);
    });
  });
});
