/**
 * Conversation Routes Tests
 */

import { Router } from "express";
import { createConversationRoutes } from "../../routes/conversation.routes";
import { ConversationController } from "../../controllers/conversation.controller";
import { AppContext } from "../../types/context.types";

// Mock the controller
jest.mock("../../controllers/conversation.controller");

// Mock validation middleware
jest.mock("@neuratalk/common", () => ({
  validateBody: jest.fn(() => jest.fn((req, res, next) => next())),
  validateParams: jest.fn(() => jest.fn((req, res, next) => next())),
  UuidParamSchema: {},
}));

// Mock schemas
jest.mock("../../schemas", () => ({
  SendMessageSchema: {},
}));

describe("createConversationRoutes", () => {
  let mockContext: AppContext;
  let mockController: jest.Mocked<ConversationController>;
  let router: Router;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock context
    mockContext = {
      redis: {
        connect: jest.fn(),
        disconnect: jest.fn(),
        get: jest.fn(),
        set: jest.fn(),
        del: jest.fn(),
        exists: jest.fn(),
        expire: jest.fn(),
        getOrSet: jest.fn(),
      },
      db: {
        connect: jest.fn(),
        disconnect: jest.fn(),
      },
      databaseService: {
        getFlow: jest.fn(),
        getFlowsByBot: jest.fn(),
        getBot: jest.fn(),
        getIntentItemByName: jest.fn(),
        getFaqItemByName: jest.fn(),
        getFaqTranslation: jest.fn(),
        getEntityByIdAndBotId: jest.fn(),
      },
      applicationRepository: {
        getFlow: jest.fn(),
        getFlowsByBot: jest.fn(),
        getBot: jest.fn(),
        getIntentItemByName: jest.fn(),
        getFaqItemByName: jest.fn(),
        getFaqTranslation: jest.fn(),
        getEntityByIdAndBotId: jest.fn(),
      },
      nluService: {
        testConnection: jest.fn(),
        parseMessage: jest.fn(),
      },
      socketService: {
        initialize: jest.fn(),
        disconnect: jest.fn(),
        emit: jest.fn(),
        on: jest.fn(),
        off: jest.fn(),
      },
    } as any;

    // Mock controller methods
    mockController = {
      sendMessage: jest.fn(),
      sendPreviewMessage: jest.fn(),
      deleteConversationCache: jest.fn(),
    } as any;

    (ConversationController as jest.MockedClass<typeof ConversationController>).mockImplementation(
      () => mockController,
    );

    router = createConversationRoutes(mockContext);
  });

  describe("router creation", () => {
    it("should create a router instance", () => {
      expect(router).toBeDefined();
      expect(typeof router).toBe("function");
    });

    it("should create ConversationController with context", () => {
      expect(ConversationController).toHaveBeenCalledWith(mockContext);
    });
  });

  describe("route registration", () => {
    let mockRouter: jest.Mocked<Router>;

    beforeEach(() => {
      mockRouter = {
        post: jest.fn(),
        delete: jest.fn(),
        get: jest.fn(),
        put: jest.fn(),
        patch: jest.fn(),
        use: jest.fn(),
      } as any;

      jest.spyOn(require("express"), "Router").mockReturnValue(mockRouter);
      createConversationRoutes(mockContext);
    });

    it("should register POST /conversations/:id/message route", () => {
      expect(mockRouter.post).toHaveBeenCalledWith(
        "/conversations/:id/message",
        expect.any(Function), // validateParams middleware
        expect.any(Function), // validateBody middleware
        mockController.sendMessage,
      );
    });

    it("should register POST /preview/conversations/:id/message route", () => {
      expect(mockRouter.post).toHaveBeenCalledWith(
        "/preview/conversations/:id/message",
        expect.any(Function), // validateParams middleware
        expect.any(Function), // validateBody middleware
        mockController.sendPreviewMessage,
      );
    });

    it("should register DELETE /conversations/:id/cache route", () => {
      expect(mockRouter.delete).toHaveBeenCalledWith(
        "/conversations/:id/cache",
        expect.any(Function), // validateParams middleware
        mockController.deleteConversationCache,
      );
    });

    it("should register exactly 3 routes", () => {
      expect(mockRouter.post).toHaveBeenCalledTimes(2);
      expect(mockRouter.delete).toHaveBeenCalledTimes(1);
    });
  });

  describe("middleware validation", () => {
    it("should use validateParams with UuidParamSchema for all routes", () => {
      const { validateParams, UuidParamSchema } = require("@neuratalk/common");

      expect(validateParams).toHaveBeenCalledWith(UuidParamSchema);
      expect(validateParams).toHaveBeenCalledTimes(3); // Called for all 3 routes
    });

    it("should use validateBody with SendMessageSchema for message routes", () => {
      const { validateBody } = require("@neuratalk/common");
      const { SendMessageSchema } = require("../../schemas");

      expect(validateBody).toHaveBeenCalledWith(SendMessageSchema);
      expect(validateBody).toHaveBeenCalledTimes(2); // Called for 2 message routes
    });
  });

  describe("route paths", () => {
    it("should have correct route paths", () => {
      const routePaths = [
        "/conversations/:id/message",
        "/preview/conversations/:id/message",
        "/conversations/:id/cache",
      ];

      // Verify that these are the expected paths
      expect(routePaths).toHaveLength(3);
      expect(routePaths).toContain("/conversations/:id/message");
      expect(routePaths).toContain("/preview/conversations/:id/message");
      expect(routePaths).toContain("/conversations/:id/cache");
    });
  });

  describe("controller method binding", () => {
    it("should bind sendMessage method correctly", () => {
      expect(mockController.sendMessage).toBeDefined();
      expect(typeof mockController.sendMessage).toBe("function");
    });

    it("should bind sendPreviewMessage method correctly", () => {
      expect(mockController.sendPreviewMessage).toBeDefined();
      expect(typeof mockController.sendPreviewMessage).toBe("function");
    });

    it("should bind deleteConversationCache method correctly", () => {
      expect(mockController.deleteConversationCache).toBeDefined();
      expect(typeof mockController.deleteConversationCache).toBe("function");
    });
  });

  describe("error handling", () => {
    it("should handle context parameter correctly", () => {
      expect(() => createConversationRoutes(mockContext)).not.toThrow();
    });

    it("should handle null context gracefully", () => {
      expect(() => createConversationRoutes(null as any)).not.toThrow();
    });

    it("should handle undefined context gracefully", () => {
      expect(() => createConversationRoutes(undefined as any)).not.toThrow();
    });
  });

  describe("return value", () => {
    it("should return a Router instance", () => {
      const result = createConversationRoutes(mockContext);
      expect(result).toBeDefined();
      expect(typeof result).toBe("object");
      // Router is an object with router methods
      expect(result).toHaveProperty("use");
      expect(result).toHaveProperty("get");
      expect(result).toHaveProperty("post");
      expect(result).toHaveProperty("delete");
    });
  });
});
