/**
 * App Tests
 */

// Mock all middleware modules before importing anything else
jest.mock("cors", () => jest.fn(() => jest.fn()));
jest.mock("helmet", () => jest.fn(() => jest.fn()));
jest.mock("compression", () => jest.fn(() => jest.fn()));
jest.mock("morgan", () => jest.fn(() => jest.fn()));
jest.mock("swagger-ui-express", () => ({
  serve: jest.fn(),
  setup: jest.fn(() => jest.fn()),
}));

// Mock dotenv
jest.mock("dotenv", () => ({
  config: jest.fn(),
}));

// Mock config and swagger
jest.mock("../config", () => {
  const mockConfig = {
    server: {
      port: 3000,
      env: "test",
      corsOrigins: ["http://localhost:3000"],
    },
    database: {
      url: "postgresql://test:test@localhost:5432/test_db",
      host: "localhost",
      port: 5432,
      name: "test_db",
      user: "test",
      password: "test",
      ssl: false,
      maxConnections: 20,
    },
    redis: {
      url: "redis://localhost:6379",
      host: "localhost",
      port: 6379,
      db: 0,
      keyPrefix: "chatbot:",
    },
    services: {
      rasaNluUrl: "http://localhost:5005",
      botBuilderUrl: "http://localhost:3000",
      chatServiceUrl: "http://localhost:3002",
    },
    security: {
      jwtSecret: "test-secret",
      apiKey: "test-api-key",
    },
    session: {
      ttlMinutes: 30,
      maxConcurrentSessions: 10,
      scriptTimeoutMs: 100,
      asyncTimeoutMs: 30000,
    },
    logging: {
      level: "info",
    },
  };

  return {
    __esModule: true,
    default: mockConfig,
  };
});
jest.mock("../config/swagger", () => ({}));

// Mock @neuratalk modules
jest.mock("@neuratalk/bot-store", () => ({
  DatabaseConnection: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
  })),
}));

jest.mock("@neuratalk/common", () => ({
  ApiResponse: {
    success: jest.fn(),
    error: jest.fn(),
  },
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock services
jest.mock("../services/redis.service", () => ({
  RedisService: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    get: jest.fn().mockResolvedValue(null),
    set: jest.fn().mockResolvedValue(undefined),
    del: jest.fn().mockResolvedValue(undefined),
    exists: jest.fn().mockResolvedValue(false),
    expire: jest.fn().mockResolvedValue(undefined),
    getOrSet: jest
      .fn()
      .mockImplementation(
        async (
          _key: string,
          fetchFn: () => Promise<any>,
          _ttl?: number,
          forceRefresh?: boolean,
        ) => {
          if (forceRefresh) {
            return await fetchFn();
          }
          return await fetchFn();
        },
      ),
  })),
}));

jest.mock("../services/nlu.service", () => ({
  NLUService: jest.fn().mockImplementation(() => ({
    testConnection: jest.fn().mockResolvedValue(true),
  })),
}));

jest.mock("../services/debugger.service", () => ({
  DebuggerService: {
    getInstance: jest.fn().mockReturnValue({
      initialize: jest.fn(),
    }),
  },
}));

jest.mock("../services/database.service", () => ({
  DatabaseService: jest.fn().mockImplementation(() => ({
    getFlow: jest.fn().mockResolvedValue(null),
    getFlowsByBot: jest.fn().mockResolvedValue([]),
    getBot: jest.fn().mockResolvedValue(null),
    getIntentItemByName: jest.fn().mockResolvedValue(null),
    getFaqItemByName: jest.fn().mockResolvedValue(null),
    getFaqTranslation: jest.fn().mockResolvedValue(null),
    getEntityByIdAndBotId: jest.fn().mockResolvedValue(null),
  })),
}));

jest.mock("../services/application-repository.service", () => ({
  ApplicationRepository: jest.fn().mockImplementation(() => ({
    getFlow: jest.fn().mockResolvedValue(null),
    getFlowsByBot: jest.fn().mockResolvedValue([]),
    getBot: jest.fn().mockResolvedValue(null),
    getIntentItemByName: jest.fn().mockResolvedValue(null),
    getFaqItemByName: jest.fn().mockResolvedValue(null),
    getFaqTranslation: jest.fn().mockResolvedValue(null),
    getEntityByIdAndBotId: jest.fn().mockResolvedValue(null),
  })),
}));

jest.mock("../services/socket.service", () => ({
  SocketService: jest.fn().mockImplementation(() => ({
    initialize: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    emit: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
  })),
}));

import { App } from "../app";
import { RedisService } from "../services/redis.service";
import { NLUService } from "../services/nlu.service";
import { DatabaseConnection } from "@neuratalk/bot-store";
import { createServer, Server as HttpServer } from "http";
import express, { Application } from "express";

// Mock express module directly
const mockExpressAppInstance: Partial<Application> = {
  use: jest.fn(),
  get: jest.fn(),
  listen: jest.fn(),
};

// Mock the express module itself, including its static methods
jest.mock("express", () => {
  const mockExpress = jest.fn(() => mockExpressAppInstance as Application); // Mock the default export (the express() function)

  // Attach static methods like .json() and .urlencoded() to the mocked express function
  (mockExpress as any).json = jest.fn(() => jest.fn()); // Return mock middleware
  (mockExpress as any).urlencoded = jest.fn(() => jest.fn()); // Return mock middleware

  return mockExpress;
});

// Mock createServer directly
const mockHttpServerInstance: Partial<HttpServer> = {
  listen: jest.fn((...args: any[]) => {
    const callback = args.find((arg) => typeof arg === "function");
    if (callback) callback();
    return mockHttpServerInstance as HttpServer; // Return the mocked server instance
  }) as any,
  close: jest.fn(), // Add close method for graceful shutdown tests
};
jest.mock("http", () => ({
  createServer: jest.fn(() => mockHttpServerInstance as HttpServer),
}));

// Mock routes
jest.mock("../routes/socket.routes", () => ({
  createSocketRoutes: jest.fn().mockImplementation(() => {
    // Mock implementation that doesn't throw
    return {};
  }),
}));
jest.mock("../routes/index.router", () => ({
  createRoutes: jest.fn().mockReturnValue([]),
}));

describe("App", () => {
  let app: App;
  let processExitSpy: jest.SpyInstance;
  let consoleErrorSpy: jest.SpyInstance;

  // Use the globally mocked instances directly
  const mockedExpress = express as jest.MockedFunction<typeof express>;
  const mockedCreateServer = createServer as jest.MockedFunction<typeof createServer>;

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();

    // Mock process.exit to prevent actual exit and capture calls
    processExitSpy = jest.spyOn(process, "exit").mockImplementation(() => {
      throw new Error("process.exit called");
    });

    // Mock console.error to capture error logs
    consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();

    // Mock process event listeners but store them for testing
    const processListeners: Map<string, Function[]> = new Map();
    jest.spyOn(process, "on").mockImplementation((event: string | symbol, listener: any) => {
      const eventName = String(event);
      if (!processListeners.has(eventName)) {
        processListeners.set(eventName, []);
      }
      processListeners.get(eventName)!.push(listener);
      return process;
    });

    // Mock process.listeners to return our stored listeners
    jest.spyOn(process, "listeners").mockImplementation((event: string | symbol) => {
      const eventName = String(event);
      return (processListeners.get(eventName) || []) as any;
    });

    // Ensure the mocked instances are clean for each test
    mockedExpress.mockClear();
    (mockedExpress.json as jest.Mock).mockClear();
    (mockedExpress.urlencoded as jest.Mock).mockClear();
    mockExpressAppInstance.use = jest.fn();
    mockExpressAppInstance.get = jest.fn();
    mockExpressAppInstance.listen = jest.fn();

    mockedCreateServer.mockClear();
    mockHttpServerInstance.listen = jest.fn((...args: any[]) => {
      const callback = args.find((arg) => typeof arg === "function");
      if (callback) callback();
      return mockHttpServerInstance as HttpServer;
    }) as any;
    mockHttpServerInstance.close = jest.fn();

    // Mock DatabaseConnection
    const mockDatabaseConnection = {
      connect: jest.fn().mockResolvedValue(undefined),
      disconnect: jest.fn().mockResolvedValue(undefined),
    };
    (DatabaseConnection as jest.Mock).mockImplementation(() => mockDatabaseConnection);

    // Mock RedisService
    const mockRedisService = {
      connect: jest.fn().mockResolvedValue(undefined),
      disconnect: jest.fn().mockResolvedValue(undefined),
    };
    (RedisService as jest.Mock).mockImplementation(() => mockRedisService);

    // Mock NLUService
    const mockNLUService = {
      testConnection: jest.fn().mockResolvedValue(true),
    };
    (NLUService as jest.Mock).mockImplementation(() => mockNLUService);

    try {
      app = new App();
    } catch (error: any) {
      if (error.message !== "process.exit called") {
        throw error;
      }
      // If process.exit was called during construction, we need to handle it
      console.warn("App constructor called process.exit, this may indicate a configuration issue");
    }
  });

  afterEach(() => {
    processExitSpy.mockRestore();
    consoleErrorSpy.mockRestore();
    jest.clearAllMocks();
  });

  describe("constructor", () => {
    it("should create express app and http server", () => {
      expect(mockedExpress).toHaveBeenCalledTimes(1); // express() should be called once
      expect(mockedCreateServer).toHaveBeenCalledTimes(1); // createServer() should be called once
      expect(mockedCreateServer).toHaveBeenCalledWith(mockExpressAppInstance); // Should be called with the mocked express app instance
    });
  });

  describe("start", () => {
    it("should initialize services and start server successfully", async () => {
      // Don't mock console.error so we can see the actual errors
      const originalConsoleError = console.error;
      let capturedErrors: any[] = [];
      console.error = (...args: any[]) => {
        capturedErrors.push(args);
        originalConsoleError(...args); // Still log to console
      };

      // Temporarily restore process.exit to see the actual error
      processExitSpy.mockRestore();
      let exitCode: number | undefined;
      processExitSpy = jest
        .spyOn(process, "exit")
        .mockImplementation((code?: string | number | null) => {
          if (typeof code === "number") {
            exitCode = code;
          } else if (code) {
            exitCode = parseInt(String(code));
          } else {
            exitCode = undefined;
          }
          throw new Error(`process.exit called with code: ${code}`);
        });

      try {
        await app.start();

        expect(mockHttpServerInstance.listen).toHaveBeenCalledWith(
          expect.any(Number),
          expect.any(Function),
        );
      } catch (error: any) {
        // Log the actual error to understand what's happening
        console.log("=== DEBUG INFO ===");
        console.log("Actual error during app start:", error);
        console.log("Captured console.error calls:", capturedErrors);
        console.log("Exit code:", exitCode);
        console.log("Error stack:", error.stack);
        console.log("==================");

        // If process.exit was called, the test should fail
        if (error.message.includes("process.exit called")) {
          const errorDetails =
            capturedErrors.length > 0
              ? JSON.stringify(capturedErrors, null, 2)
              : "No error details captured";
          throw new Error(
            `App failed to start properly. Exit code: ${exitCode}. Error details: ${errorDetails}`,
          );
        }
        throw error;
      } finally {
        console.error = originalConsoleError;
      }
    });

    it("should handle initialization errors", async () => {
      const error = new Error("Initialization failed");
      const mockDatabaseConnection = {
        connect: jest.fn().mockRejectedValue(error),
      };
      (DatabaseConnection as jest.Mock).mockImplementation(() => mockDatabaseConnection);

      await expect(app.start()).rejects.toThrow("process.exit called");
      expect(processExitSpy).toHaveBeenCalledWith(1);
    });
  });

  describe("stop", () => {
    it("should disconnect services gracefully", async () => {
      // First start the app to initialize services
      await app.start();

      // Mock context services for stop method
      (app as any).context = {
        redis: { disconnect: jest.fn().mockResolvedValue(undefined) },
        db: { disconnect: jest.fn().mockResolvedValue(undefined) },
        socketService: { disconnect: jest.fn().mockResolvedValue(undefined) },
      };

      await app.stop();

      expect((app as any).context.redis.disconnect).toHaveBeenCalledTimes(1);
      expect((app as any).context.db.disconnect).toHaveBeenCalledTimes(1);
      expect((app as any).context.socketService.disconnect).toHaveBeenCalledTimes(1);
    });

    it("should handle shutdown errors gracefully", async () => {
      // Mock logger.error instead of console.error
      const { logger } = require("@neuratalk/common");
      const loggerErrorSpy = jest.spyOn(logger, "error").mockImplementation();

      // Mock context to have services that throw errors on disconnect
      (app as any).context = {
        redis: {
          disconnect: jest.fn().mockRejectedValue(new Error("Redis disconnect failed")),
        },
        db: {
          disconnect: jest.fn().mockRejectedValue(new Error("DB disconnect failed")),
        },
        socketService: {
          disconnect: jest.fn().mockRejectedValue(new Error("Socket disconnect failed")),
        },
      };

      await app.stop();

      expect(loggerErrorSpy).toHaveBeenCalledWith("Error during shutdown:", expect.any(Error));
      loggerErrorSpy.mockRestore();
    });
  });

  describe("middleware initialization", () => {
    it("should setup security middleware", async () => {
      await app.start();

      // Verify that middleware setup methods were called
      expect(mockExpressAppInstance.use).toHaveBeenCalledWith(expect.any(Function));
    });

    it("should setup request parsing middleware", async () => {
      await app.start();

      expect(mockExpressAppInstance.use).toHaveBeenCalledWith(expect.any(Function));
      expect(mockedExpress.json).toHaveBeenCalledTimes(1);
      expect(mockedExpress.urlencoded).toHaveBeenCalledTimes(1);
    });

    it("should setup logging middleware in non-test environment", async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = "development";

      await app.start();

      expect(mockExpressAppInstance.use).toHaveBeenCalledWith(expect.any(Function));

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe("routes initialization", () => {
    it("should setup API routes", async () => {
      await app.start();

      // Check that app.use was called with "/api/v1" as the first argument
      const calls = (mockExpressAppInstance.use as jest.Mock).mock.calls;
      const apiV1Call = calls.find((call) => call[0] === "/api/v1");
      expect(apiV1Call).toBeDefined();
    });

    it("should setup swagger documentation", async () => {
      await app.start();

      expect(mockExpressAppInstance.use).toHaveBeenCalledWith(
        "/api-docs",
        expect.any(Function),
        expect.any(Function),
      );
      expect(mockExpressAppInstance.get).toHaveBeenCalledWith(
        "/api-docs.json",
        expect.any(Function),
      );
    });

    it("should setup root endpoint", async () => {
      await app.start();

      expect(mockExpressAppInstance.get).toHaveBeenCalledWith("/", expect.any(Function));
    });

    it("should setup 404 handler", async () => {
      await app.start();

      expect(mockExpressAppInstance.use).toHaveBeenCalledWith("*", expect.any(Function));
    });
  });

  describe("error handling", () => {
    it("should setup global error handler", async () => {
      await app.start();

      expect(mockExpressAppInstance.use).toHaveBeenCalledWith(expect.any(Function));
    });

    it("should handle uncaught exceptions", async () => {
      const processExitSpy = jest.spyOn(process, "exit").mockImplementation(() => {
        throw new Error("process.exit called");
      });

      await app.start();

      // Simulate uncaught exception
      const uncaughtExceptionHandler = process.listeners("uncaughtException").pop() as Function;
      expect(() => uncaughtExceptionHandler(new Error("Test error"))).toThrow(
        "process.exit called",
      );

      processExitSpy.mockRestore();
    });

    it("should handle unhandled promise rejections", async () => {
      const processExitSpy = jest.spyOn(process, "exit").mockImplementation(() => {
        throw new Error("process.exit called");
      });

      await app.start();

      // Simulate unhandled rejection
      const unhandledRejectionHandler = process.listeners("unhandledRejection").pop() as Function;
      expect(() => unhandledRejectionHandler("Test reason", Promise.resolve())).toThrow(
        "process.exit called",
      );

      processExitSpy.mockRestore();
    });
  });

  describe("request middleware", () => {
    it("should add request ID to requests", async () => {
      await app.start();

      // Find the request ID middleware
      const middlewareCalls = (mockExpressAppInstance.use as jest.Mock).mock.calls;
      const requestIdMiddleware = middlewareCalls.find(
        (call: any) => call.length === 1 && typeof call[0] === "function",
      );

      expect(requestIdMiddleware).toBeDefined();
    });

    it("should add request logging middleware", async () => {
      await app.start();

      // Verify logging middleware was added
      const middlewareCalls = (mockExpressAppInstance.use as jest.Mock).mock.calls;
      const loggingMiddleware = middlewareCalls.find(
        (call: any) => call.length === 1 && typeof call[0] === "function",
      );

      expect(loggingMiddleware).toBeDefined();
    });
  });
});
