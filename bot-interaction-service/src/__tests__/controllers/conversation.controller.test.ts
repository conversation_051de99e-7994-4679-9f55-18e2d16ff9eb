import { Request, Response } from "express";
import { ConversationController } from "../../controllers/conversation.controller";
import { FlowEngine } from "../../engine/flow-engine";
import { RedisService } from "../../services/redis.service";
import { AppContext } from "../../types/context.types";
import { logger, successResponse, errorResponse } from "@neuratalk/common";
import { SendMessageParam, SendMessageRequest } from "../../schemas";
import { OutgoingMessage } from "../../types";
import { FlowNode, MessageNodeType } from "../../types/enum";
import { ChannelType } from "../../types/conversation.types"; // Corrected import path for ChannelType

// Mock external modules and services
jest.mock("../../engine/flow-engine");
jest.mock("../../services/redis.service");
jest.mock("@neuratalk/common", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  successResponse: jest.fn((data) => ({ success: true, data })),
  errorResponse: jest.fn((error) => ({ success: false, error })),
}));

describe("ConversationController", () => {
  let controller: ConversationController;
  let mockRedisService: any; // Changed to any to simplify mocking
  let mockFlowEngine: jest.Mocked<FlowEngine>;
  let mockAppContext: AppContext;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockStatus: jest.Mock;
  let mockJson: jest.Mock;

  const CONVERSATION_ID = "conv-123e4567-e89b-12d3-a456-426614174000";
  const BOT_ID = "bot-123e4567-e89b-12d3-a456-426614174000";

  beforeEach(() => {
    jest.clearAllMocks();

    mockRedisService = {
      deleteConversationContext: jest.fn(),
      getConversationContext: jest.fn(),
      setConversationContext: jest.fn(),
      updateConversationContext: jest.fn(),
      getOrSet: jest.fn(),
      connect: jest.fn(),
      disconnect: jest.fn(),
      healthCheck: jest.fn(),
      clearAll: jest.fn(),
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      getFlow: jest.fn(),
      setFlow: jest.fn(),
      invalidateFlow: jest.fn(),
      connected: true,
    };

    mockFlowEngine = {
      processMessage: jest.fn(),
    } as any; // Cast to any because FlowEngine is a singleton and getInstance is mocked

    (FlowEngine.getInstance as jest.Mock).mockReturnValue(mockFlowEngine);

    mockAppContext = {
      redis: mockRedisService,
      // Other context properties are not used by ConversationController directly, but required by AppContext type
      db: {} as any,
      nlu: {} as any,
      applicationRepository: {} as any,
      debuggerService: {} as any,
      socketService: {} as any,
    };

    controller = new ConversationController(mockAppContext);

    mockStatus = jest.fn().mockReturnThis();
    mockJson = jest.fn();
    mockResponse = {
      status: mockStatus,
      json: mockJson,
    };
  });

  describe("sendMessage", () => {
    it("should process message successfully and return 200", async () => {
      const requestBody: SendMessageRequest = {
        botId: BOT_ID,
        content: "Hello",
        messageType: MessageNodeType.TEXT,
        channelType: ChannelType.WEB, // Added missing channelType
      };
      mockRequest = {
        params: { id: CONVERSATION_ID } as SendMessageParam,
        body: requestBody,
      };

      const mockMessages: OutgoingMessage[] = [
        {
          nodeType: FlowNode.MESSAGE,
          data: { text: "Bot response", type: MessageNodeType.TEXT },
        },
      ];
      mockFlowEngine.processMessage.mockResolvedValueOnce({
        success: true,
        messages: mockMessages,
        shouldPause: false,
      });

      await controller.sendMessage(
        mockRequest as Request<SendMessageParam, any, SendMessageRequest>,
        mockResponse as Response,
      );

      expect(mockFlowEngine.processMessage).toHaveBeenCalledWith(
        CONVERSATION_ID,
        requestBody,
        false,
      );
      expect(successResponse).toHaveBeenCalledWith({
        conversationId: CONVERSATION_ID,
        response: mockMessages,
      });
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({ success: true, data: expect.any(Object) }),
      );
      expect(mockStatus).not.toHaveBeenCalled(); // Default status is 200
    });

    it("should handle flow execution failure and return 500", async () => {
      const requestBody: SendMessageRequest = {
        botId: BOT_ID,
        content: "Hello",
        messageType: MessageNodeType.TEXT,
        channelType: ChannelType.WEB, // Added missing channelType
      };
      mockRequest = {
        params: { id: CONVERSATION_ID } as SendMessageParam,
        body: requestBody,
      };

      const errorMessage = "Flow error";
      mockFlowEngine.processMessage.mockResolvedValueOnce({
        success: false,
        messages: [],
        shouldPause: false,
        error: errorMessage,
      });

      await controller.sendMessage(
        mockRequest as Request<SendMessageParam, any, SendMessageRequest>,
        mockResponse as Response,
      );

      expect(mockFlowEngine.processMessage).toHaveBeenCalledWith(
        CONVERSATION_ID,
        requestBody,
        false,
      );
      expect(errorResponse).toHaveBeenCalledWith({
        error: errorMessage,
        code: "FLOW_EXECUTION_ERROR",
        message: errorMessage,
      });
      expect(mockStatus).toHaveBeenCalledWith(500);
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({ success: false, error: expect.any(Object) }),
      );
    });

    it("should handle general errors during message processing and return 500", async () => {
      const requestBody: SendMessageRequest = {
        botId: BOT_ID,
        content: "Hello",
        messageType: MessageNodeType.TEXT,
        channelType: ChannelType.WEB, // Added missing channelType
      };
      mockRequest = {
        params: { id: CONVERSATION_ID } as SendMessageParam,
        body: requestBody,
      };

      const thrownError = new Error("Something went wrong");
      mockFlowEngine.processMessage.mockRejectedValueOnce(thrownError);

      await controller.sendMessage(
        mockRequest as Request<SendMessageParam, any, SendMessageRequest>,
        mockResponse as Response,
      );

      expect(mockFlowEngine.processMessage).toHaveBeenCalledWith(
        CONVERSATION_ID,
        requestBody,
        false,
      );
      expect(errorResponse).toHaveBeenCalledWith({
        error: thrownError,
        code: "INTERNAL_ERROR",
        message: "An internal error occurred while processing your message",
      });
      expect(mockStatus).toHaveBeenCalledWith(500);
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({ success: false, error: expect.any(Object) }),
      );
    });

    it("should return default message if flow engine returns no messages on success", async () => {
      const requestBody: SendMessageRequest = {
        botId: BOT_ID,
        content: "Hello",
        messageType: MessageNodeType.TEXT,
        channelType: ChannelType.WEB, // Added missing channelType
      };
      mockRequest = {
        params: { id: CONVERSATION_ID } as SendMessageParam,
        body: requestBody,
      };

      mockFlowEngine.processMessage.mockResolvedValueOnce({
        success: true,
        messages: [], // No messages returned
        shouldPause: false,
      });

      await controller.sendMessage(
        mockRequest as Request<SendMessageParam, any, SendMessageRequest>,
        mockResponse as Response,
      );

      expect(successResponse).toHaveBeenCalledWith({
        conversationId: CONVERSATION_ID,
        response: [
          {
            conversationId: CONVERSATION_ID,
            content: `Message processed successfully in mode`,
            messageType: MessageNodeType.TEXT,
          },
        ],
      });
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({ success: true, data: expect.any(Object) }),
      );
    });
  });

  describe("sendPreviewMessage", () => {
    it("should process message successfully in preview mode and return 200", async () => {
      const requestBody: SendMessageRequest = {
        botId: BOT_ID,
        content: "Hello preview",
        messageType: MessageNodeType.TEXT,
        channelType: ChannelType.WEB, // Added missing channelType
      };
      mockRequest = {
        params: { id: CONVERSATION_ID } as SendMessageParam,
        body: requestBody,
      };

      const mockMessages: OutgoingMessage[] = [
        {
          nodeType: FlowNode.MESSAGE,
          data: { text: "Bot preview response", type: MessageNodeType.TEXT },
        },
      ];
      mockFlowEngine.processMessage.mockResolvedValueOnce({
        success: true,
        messages: mockMessages,
        shouldPause: false,
      });

      await controller.sendPreviewMessage(
        mockRequest as Request<SendMessageParam, any, SendMessageRequest>,
        mockResponse as Response,
      );

      expect(mockFlowEngine.processMessage).toHaveBeenCalledWith(
        CONVERSATION_ID,
        requestBody,
        true, // isPreview should be true
      );
      expect(successResponse).toHaveBeenCalledWith({
        conversationId: CONVERSATION_ID,
        response: mockMessages,
      });
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({ success: true, data: expect.any(Object) }),
      );
    });

    it("should return default message if flow engine returns no messages on success in preview mode", async () => {
      const requestBody: SendMessageRequest = {
        botId: BOT_ID,
        content: "Hello preview",
        messageType: MessageNodeType.TEXT,
        channelType: ChannelType.WEB, // Added missing channelType
      };
      mockRequest = {
        params: { id: CONVERSATION_ID } as SendMessageParam,
        body: requestBody,
      };

      mockFlowEngine.processMessage.mockResolvedValueOnce({
        success: true,
        messages: [], // No messages returned
        shouldPause: false,
      });

      await controller.sendPreviewMessage(
        mockRequest as Request<SendMessageParam, any, SendMessageRequest>,
        mockResponse as Response,
      );

      expect(successResponse).toHaveBeenCalledWith({
        conversationId: CONVERSATION_ID,
        response: [
          {
            conversationId: CONVERSATION_ID,
            content: `Message processed successfully in preview mode`,
            messageType: MessageNodeType.TEXT,
          },
        ],
      });
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({ success: true, data: expect.any(Object) }),
      );
    });
  });

  describe("deleteConversationCache", () => {
    it("should clear cache successfully and return 200", async () => {
      mockRequest = {
        params: { id: CONVERSATION_ID },
      };
      mockRedisService.deleteConversationContext.mockResolvedValueOnce(true);

      await controller.deleteConversationCache(mockRequest as Request, mockResponse as Response);

      expect(mockRedisService.deleteConversationContext).toHaveBeenCalledWith(CONVERSATION_ID);
      expect(successResponse).toHaveBeenCalledWith({
        message: `Cache cleared for conversation ${CONVERSATION_ID}`,
      });
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({ success: true, data: expect.any(Object) }),
      );
      expect(mockStatus).not.toHaveBeenCalled(); // Default status is 200
    });

    it("should return 404 if conversation context not found", async () => {
      mockRequest = {
        params: { id: CONVERSATION_ID },
      };
      mockRedisService.deleteConversationContext.mockResolvedValueOnce(false);

      await controller.deleteConversationCache(mockRequest as Request, mockResponse as Response);

      expect(mockRedisService.deleteConversationContext).toHaveBeenCalledWith(CONVERSATION_ID);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "CONTEXT_NOT_FOUND",
        message: "Conversation context not found or already cleared",
      });
      expect(mockStatus).toHaveBeenCalledWith(404);
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({ success: false, error: expect.any(Object) }),
      );
    });

    it("should return 400 if conversation ID is missing", async () => {
      mockRequest = {
        params: { id: undefined as any }, // Cast to any to allow undefined
      };

      await controller.deleteConversationCache(mockRequest as Request, mockResponse as Response);

      expect(mockRedisService.deleteConversationContext).not.toHaveBeenCalled();
      expect(errorResponse).toHaveBeenCalledWith({
        code: "MISSING_CONVERSATION_ID",
        message: "Conversation ID is required",
      });
      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({ success: false, error: expect.any(Object) }),
      );
    });

    it("should handle general errors during cache deletion and return 500", async () => {
      mockRequest = {
        params: { id: CONVERSATION_ID },
      };
      const thrownError = new Error("Redis error");
      mockRedisService.deleteConversationContext.mockRejectedValueOnce(thrownError);

      await controller.deleteConversationCache(mockRequest as Request, mockResponse as Response);

      expect(mockRedisService.deleteConversationContext).toHaveBeenCalledWith(CONVERSATION_ID);
      expect(errorResponse).toHaveBeenCalledWith({
        error: thrownError,
        code: "INTERNAL_ERROR",
        message: "An internal error occurred while clearing cache",
      });
      expect(mockStatus).toHaveBeenCalledWith(500);
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({ success: false, error: expect.any(Object) }),
      );
    });
  });
});
