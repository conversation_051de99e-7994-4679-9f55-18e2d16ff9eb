/**
 * Conversation Controller Tests
 */

import { Request, Response } from "express";
import { ConversationController } from "../../controllers/conversation.controller";
import { FlowEngine } from "../../engine/flow-engine";
import { RedisService } from "../../services/redis.service";
import { AppContext } from "../../types/context.types";
import { SendMessageRequest, SendMessageParam } from "../../schemas";
import { logger, successResponse, errorResponse } from "@neuratalk/common";
import { FlowNode, MessageNodeType } from "../../types/enum";
import { ChannelType } from "../../types/conversation.types";

// Mock dependencies
jest.mock("../../engine/flow-engine");
jest.mock("../../services/redis.service");
jest.mock("@neuratalk/common", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  successResponse: jest.fn(),
  errorResponse: jest.fn(),
}));

describe("ConversationController", () => {
  let controller: ConversationController;
  let mockContext: AppContext;
  let mockRedisService: jest.Mocked<RedisService>;
  let mockFlowEngine: jest.Mocked<FlowEngine>;
  let mockReq: Partial<Request<SendMessageParam, any, SendMessageRequest>>;
  let mockRes: Partial<Response>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock Redis service
    mockRedisService = {
      connect: jest.fn(),
      disconnect: jest.fn(),
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      exists: jest.fn(),
      expire: jest.fn(),
      getOrSet: jest.fn(),
      deleteConversationContext: jest.fn(),
      getConversationContext: jest.fn(),
      setConversationContext: jest.fn(),
      updateConversationContext: jest.fn(),
      clearAll: jest.fn(),
      healthCheck: jest.fn(),
    } as any;

    // Mock Flow engine
    mockFlowEngine = {
      processMessage: jest.fn(),
      getInstance: jest.fn(),
    } as any;

    // Mock context
    mockContext = {
      redis: mockRedisService,
      db: {
        connect: jest.fn(),
        disconnect: jest.fn(),
      },
      nlu: {
        testConnection: jest.fn(),
        parseMessage: jest.fn(),
      },
      databaseService: {
        getFlow: jest.fn(),
        getFlowsByBot: jest.fn(),
        getBot: jest.fn(),
        getIntentItemByName: jest.fn(),
        getFaqItemByName: jest.fn(),
        getFaqTranslation: jest.fn(),
        getEntityByIdAndBotId: jest.fn(),
      },
      applicationRepository: {
        getFlow: jest.fn(),
        getFlowsByBot: jest.fn(),
        getBot: jest.fn(),
        getIntentItemByName: jest.fn(),
        getFaqItemByName: jest.fn(),
        getFaqTranslation: jest.fn(),
        getEntityByIdAndBotId: jest.fn(),
      },
      nluService: {
        testConnection: jest.fn(),
        parseMessage: jest.fn(),
      },
      socketService: {
        initialize: jest.fn(),
        disconnect: jest.fn(),
        emit: jest.fn(),
        on: jest.fn(),
        off: jest.fn(),
      },
    } as any;

    // Mock FlowEngine.getInstance
    (FlowEngine.getInstance as jest.Mock).mockReturnValue(mockFlowEngine);

    // Mock request and response objects
    mockReq = {
      params: { id: "test-conversation-id" },
      body: {
        botId: "test-bot-id",
        content: "Hello, world!",
        messageType: MessageNodeType.TEXT,
        channelType: ChannelType.WEB,
        metadata: { userId: "user123" },
      },
    };

    mockRes = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
    };

    // Mock response helpers
    (successResponse as jest.Mock).mockImplementation((data) => ({ success: true, data }));
    (errorResponse as jest.Mock).mockImplementation((error) => ({ success: false, error }));

    controller = new ConversationController(mockContext);
  });

  describe("constructor", () => {
    it("should initialize with context dependencies", () => {
      expect(FlowEngine.getInstance).toHaveBeenCalledWith(mockContext);
      expect(controller).toBeDefined();
    });

    it("should set redis service from context", () => {
      expect((controller as any).redisService).toBe(mockRedisService);
    });

    it("should set flow engine from getInstance", () => {
      expect((controller as any).flowEngine).toBe(mockFlowEngine);
    });
  });

  describe("sendMessage", () => {
    it("should process message successfully", async () => {
      const mockResult = {
        success: true,
        messages: [
          {
            nodeType: FlowNode.MESSAGE,
            data: {
              text: "Hello! How can I help you?",
              type: MessageNodeType.TEXT,
            },
          } as any,
        ],
        shouldPause: false,
      };

      mockFlowEngine.processMessage.mockResolvedValue(mockResult);

      await controller.sendMessage(mockReq as any, mockRes as any);

      expect(mockFlowEngine.processMessage).toHaveBeenCalledWith(
        "test-conversation-id",
        mockReq.body,
        false,
      );
      expect(logger.info).toHaveBeenCalledWith(
        'Processing message from conversation test-conversation-id: "Hello, world!"',
      );
      expect(successResponse).toHaveBeenCalledWith({
        conversationId: "test-conversation-id",
        response: mockResult.messages,
      });
      expect(mockRes.json).toHaveBeenCalled();
    });

    it("should handle flow execution failure", async () => {
      const mockResult = {
        success: false,
        messages: [],
        shouldPause: false,
        error: "Flow execution failed",
      };

      mockFlowEngine.processMessage.mockResolvedValue(mockResult);

      await controller.sendMessage(mockReq as any, mockRes as any);

      expect(logger.error).toHaveBeenCalledWith(
        "Flow execution failed for conversation test-conversation-id in mode: Flow execution failed",
      );
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: "Flow execution failed",
        code: "FLOW_EXECUTION_ERROR",
        message: "Flow execution failed",
      });
    });

    it("should handle exceptions", async () => {
      const error = new Error("Unexpected error");
      mockFlowEngine.processMessage.mockRejectedValue(error);

      await controller.sendMessage(mockReq as any, mockRes as any);

      expect(logger.error).toHaveBeenCalledWith("Error in sendMessage controller:", error);
      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error,
        code: "INTERNAL_ERROR",
        message: "An internal error occurred while processing your message",
      });
    });

    it("should use default response when no messages returned", async () => {
      const mockResult = {
        success: true,
        messages: [], // Empty array to trigger default response
        shouldPause: false,
      };

      mockFlowEngine.processMessage.mockResolvedValue(mockResult);

      await controller.sendMessage(mockReq as any, mockRes as any);

      expect(successResponse).toHaveBeenCalledWith({
        conversationId: "test-conversation-id",
        response: [
          {
            nodeType: FlowNode.MESSAGE,
            data: {
              text: "Message processed successfully in mode",
              type: MessageNodeType.TEXT,
            },
          },
        ],
      });
    });
  });

  describe("sendPreviewMessage", () => {
    it("should process preview message successfully", async () => {
      const mockResult = {
        success: true,
        messages: [
          {
            nodeType: FlowNode.MESSAGE,
            data: {
              text: "Preview response",
              type: MessageNodeType.TEXT,
            },
          } as any,
        ],
        shouldPause: false,
      };

      mockFlowEngine.processMessage.mockResolvedValue(mockResult);

      await controller.sendPreviewMessage(mockReq as any, mockRes as any);

      expect(mockFlowEngine.processMessage).toHaveBeenCalledWith(
        "test-conversation-id",
        mockReq.body,
        true,
      );
      expect(logger.info).toHaveBeenCalledWith(
        'Processing preview message from conversation test-conversation-id: "Hello, world!"',
      );
    });

    it("should handle preview mode flow execution failure", async () => {
      const mockResult = {
        success: false,
        messages: [],
        shouldPause: false,
        error: "Preview flow failed",
      };

      mockFlowEngine.processMessage.mockResolvedValue(mockResult);

      await controller.sendPreviewMessage(mockReq as any, mockRes as any);

      expect(logger.error).toHaveBeenCalledWith(
        "Flow execution failed for conversation test-conversation-id in preview mode: Preview flow failed",
      );
      expect(errorResponse).toHaveBeenCalledWith({
        error: "Preview flow failed",
        code: "FLOW_EXECUTION_ERROR",
        message: "Preview flow failed",
      });
    });

    it("should handle preview mode exceptions", async () => {
      const error = new Error("Preview error");
      mockFlowEngine.processMessage.mockRejectedValue(error);

      await controller.sendPreviewMessage(mockReq as any, mockRes as any);

      expect(logger.error).toHaveBeenCalledWith("Error in preview sendMessage controller:", error);
      expect(errorResponse).toHaveBeenCalledWith({
        error,
        code: "INTERNAL_ERROR",
        message: "An internal error occurred while processing your preview message",
      });
    });
  });
});
