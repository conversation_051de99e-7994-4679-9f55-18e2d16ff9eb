import { Router } from "express";
import { ConversationController } from "../controllers/conversation.controller";

import { validateBody, validateParams } from "@neuratalk/common";
import { SendMessageSchema } from "../schemas";
import { UuidParamSchema } from "@neuratalk/common";
import { AppContext } from "../types/context.types";

export function createConversationRoutes(context: AppContext): Router {
  const router = Router();
  const conversationController = new ConversationController(context);

  /**
   * POST /api/v1/conversations/{id}/message
   * Send a message to a conversation
   */
  router.post(
    "/conversations/:id/message",
    validateParams(UuidParamSchema),
    validate<PERSON><PERSON>(SendMessageSchema),
    conversationController.sendMessage,
  );

  /**
   * POST /api/v1/preview/conversations/{id}/message
   * Send a message to a conversation in preview mode
   */
  router.post(
    "/preview/conversations/:id/message",
    validateParams(UuidParamSchema),
    validate<PERSON><PERSON>(SendMessageSchema),
    conversationController.sendPreviewMessage,
  );

  /**
   * DELETE /api/v1/conversations/{id}/cache
   * Clear conversation cache for a specific conversation
   */
  router.delete(
    "/conversations/:id/cache",
    validateParams(UuidParamSchema),
    conversationController.deleteConversationCache,
  );

  return router;
}
