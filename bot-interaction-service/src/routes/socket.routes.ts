import { AppContext } from "../types/context.types";
import { ConversationSocketController } from "../controllers/conversation.socket.controller";
import { DebuggerSocketController } from "../controllers/debugger.socket.controller";
import { SocketEvents } from "../constants/socket.events";
import { Socket } from "socket.io";
import { validateSocketPayloadHOC } from "../utils/socket-validator";
import {
  ConversationIdPayloadSchema,
  ConversationIdPayload,
  SocketMessagePayloadSchema,
  SocketMessagePayload,
} from "../schemas/conversation.schemas";

export function createSocketRoutes(context: AppContext): void {
  const socketService = context.socketService;

  const conversationSocketController = new ConversationSocketController(context);
  const debuggerSocketController = new DebuggerSocketController(context);

  socketService.io.on(SocketEvents.CONNECTION, (socket: Socket) => {
    // Conversation-related events
    socket.on(
      SocketEvents.JOIN_CONVERSATION,
      validateSocketPayloadHOC(
        ConversationIdPayloadSchema,
        socket,
        SocketEvents.JOIN_CONVERSATION,
      )((parsedData: ConversationIdPayload) =>
        conversationSocketController.handleJoinConversation(socket, parsedData.conversationId),
      ),
    );

    socket.on(
      SocketEvents.SEND_MESSAGE,
      validateSocketPayloadHOC(
        SocketMessagePayloadSchema,
        socket,
        SocketEvents.SEND_MESSAGE,
      )((parsedData: SocketMessagePayload) =>
        conversationSocketController.handleSendMessage(socket, parsedData),
      ),
    );

    socket.on(
      SocketEvents.SEND_PREVIEW_MESSAGE,
      validateSocketPayloadHOC(
        SocketMessagePayloadSchema,
        socket,
        SocketEvents.SEND_PREVIEW_MESSAGE,
      )((parsedData: SocketMessagePayload) =>
        conversationSocketController.handleSendPreviewMessage(socket, parsedData),
      ),
    );

    socket.on(
      SocketEvents.DELETE_CONVERSATION_CACHE,
      validateSocketPayloadHOC(
        ConversationIdPayloadSchema,
        socket,
        SocketEvents.DELETE_CONVERSATION_CACHE,
      )(conversationSocketController.handleDeleteConversationCache),
    );

    // Debugger-related events
    socket.on(
      SocketEvents.DEBUGGER_SUBSCRIBE,
      validateSocketPayloadHOC(
        ConversationIdPayloadSchema,
        socket,
        SocketEvents.DEBUGGER_SUBSCRIBE,
      )((parsedData: ConversationIdPayload) =>
        debuggerSocketController.handleDebuggerSubscribe(socket, parsedData),
      ),
    );

    socket.on(
      SocketEvents.DEBUGGER_UNSUBSCRIBE,
      validateSocketPayloadHOC(
        ConversationIdPayloadSchema,
        socket,
        SocketEvents.DEBUGGER_UNSUBSCRIBE,
      )((parsedData: ConversationIdPayload) =>
        debuggerSocketController.handleDebuggerUnsubscribe(socket, parsedData),
      ),
    );

    // General socket events (DISCONNECT, ERROR) are handled directly in SocketService
  });
}