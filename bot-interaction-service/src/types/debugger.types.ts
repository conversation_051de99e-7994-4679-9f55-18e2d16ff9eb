import { NLUResponse } from "./message.types";
import { ConversationContext } from "./conversation.types";

export enum DebuggerEventType {
  LOG = "log",
  NLU_LOG = "nlu_log",
  CONTEXT = "context",
}

export enum LogType {
  INFO = "info",
  WARNING = "warning",
  ERROR = "error",
}

export interface LogPayload {
  level: LogType;
  message: string;
  details?: any;
}

export type NluLogPayload = Omit<NLUResponse, "conversationId">;

export type ContextPayload = ConversationContext;

export type DebuggerEvent =
  | {
      type: DebuggerEventType.LOG;
      timestamp: string;
      conversationId: string;
      payload: LogPayload;
    }
  | {
      type: DebuggerEventType.NLU_LOG;
      timestamp: string;
      conversationId: string;
      payload: NluLogPayload;
    }
  | {
      type: DebuggerEventType.CONTEXT;
      timestamp: string;
      conversationId: string;
      payload: ContextPayload;
    };
