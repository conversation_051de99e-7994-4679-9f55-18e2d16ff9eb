/**
 * Conversation Controller
 *
 * Handles HTTP requests for conversation management and message processing.
 * Implements the main API endpoints for the bot-interaction-service.
 *
 * @swagger
 * components:
 *   schemas:
 *     ConversationResponse:
 *       allOf:
 *         - $ref: '#/components/schemas/ApiResponse'
 *         - type: object
 *           properties:
 *             data:
 *               $ref: '#/components/schemas/SendMessageResponse'
 *     StreamEvent:
 *       type: object
 *       properties:
 *         type:
 *           type: string
 *           enum: [connected, message, heartbeat]
 *         conversationId:
 *           type: string
 *         timestamp:
 *           type: string
 *           format: date-time
 *         message:
 *           $ref: '#/components/schemas/Message'
 */

import { Request, Response } from "express";
import { FlowEngine } from "../engine/flow-engine";
import { RedisService } from "../services/redis.service";
import { SendMessageResponse } from "../types";
import { SendMessageParam, SendMessageRequest } from "../schemas";
import { logger, successResponse, errorResponse } from "@neuratalk/common";

import { AppContext } from "../types/context.types";
import { FlowNode, MessageNodeType } from "../types/enum";

export class ConversationController {
  private flowEngine: FlowEngine;
  private redisService: RedisService;

  constructor(context: AppContext) {
    this.redisService = context.redis;
    this.flowEngine = FlowEngine.getInstance(context);
  }

  private async handleMessage(
    req: Request<SendMessageParam, any, SendMessageRequest>,
    res: Response,
    isPreview: boolean,
  ): Promise<void> {
    const mode = isPreview ? "preview " : "";
    try {
      const conversationId = req.params.id;
      const { content }: SendMessageRequest = req.body;

      logger.info(`Processing ${mode}message from conversation ${conversationId}: "${content}"`);

      // Process the message through the flow engine
      const result = await this.flowEngine.processMessage(conversationId, req.body, isPreview);

      if (!result.success) {
        logger.error(
          `Flow execution failed for conversation ${conversationId} in ${mode}mode: ${result.error}`,
        );
        res.status(500).json(
          errorResponse({
            error: result.error,
            code: "FLOW_EXECUTION_ERROR",
            message: result.error || `Failed to process message in ${mode}mode`,
          }),
        );
        return;
      }

      const response: SendMessageResponse = {
        conversationId,
        response:
          result.messages && result.messages.length > 0
            ? result.messages
            : [
                {
                  nodeType: FlowNode.MESSAGE,
                  data: {
                    text: `Message processed successfully in ${mode}mode`,
                    type: MessageNodeType.TEXT,
                  },
                },
              ],
      };

      res.json(successResponse(response));
    } catch (error) {
      logger.error(`Error in ${mode}sendMessage controller:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: `An internal error occurred while processing your ${mode}message`,
        }),
      );
    }
  }

  /**
   * @swagger
   * /api/v1/conversations/{id}/message:
   *   post:
   *     summary: Send a message to a conversation
   *     description: Processes a user message through the conversation flow engine
   *     tags: [Conversations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Conversation unique identifier
   *         example: "conv-123e4567-e89b-12d3-a456-************"
   *       - in: header
   *         name: X-Bot-Id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot identifier for conversation context
   *         example: "bot-123e4567-e89b-12d3-a456-************"
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/SendMessageRequest'
   *           example:
   *             content: "Hello, I need help with my order"
   *             messageType: "text"
   *             metadata:
   *               userId: "user123"
   *               channel: "web"
   *     responses:
   *       200:
   *         description: Message processed successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/SendMessageResponse'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Flow execution error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */

  sendMessage = async (
    req: Request<SendMessageParam, any, SendMessageRequest>,
    res: Response,
  ): Promise<void> => {
    await this.handleMessage(req, res, false);
  };

  /**
   * @swagger
   * /api/v1/preview/conversations/{id}/message:
   *   post:
   *     summary: Send a message to a conversation in preview mode (bypasses Redis cache)
   *     description: Processes a user message through the conversation flow engine, forcing a refresh of ApplicationRepository data.
   *     tags: [Conversations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Conversation unique identifier
   *         example: "conv-123e4567-e89b-12d3-a456-************"
   *       - in: header
   *         name: X-Bot-Id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Bot identifier for conversation context
   *         example: "bot-123e4567-e89b-12d3-a456-************"
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/SendMessageRequest'
   *           example:
   *             content: "Hello, I need help with my order"
   *             messageType: "text"
   *             metadata:
   *               userId: "user123"
   *               channel: "web"
   *     responses:
   *       200:
   *         description: Message processed successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponse'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/SendMessageResponse'
   *       400:
   *         description: Invalid request data
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Flow execution error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  sendPreviewMessage = async (
    req: Request<SendMessageParam, any, SendMessageRequest>,
    res: Response,
  ): Promise<void> => {
    await this.handleMessage(req, res, true);
  };

  /**
   * @swagger
   * /api/v1/conversations/{id}/cache:
   *   delete:
   *     summary: Clear conversation cache
   *     description: Clears the Redis cache for a specific conversation
   *     tags: [Conversations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: Conversation unique identifier
   *         example: "conv-123e4567-e89b-12d3-a456-************"
   *     responses:
   *       200:
   *         description: Conversation cache cleared successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       400:
   *         description: Invalid conversation ID
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  deleteConversationCache = async (req: Request, res: Response): Promise<void> => {
    try {
      const conversationId = req.params.id;

      if (!conversationId) {
        res.status(400).json(
          errorResponse({
            code: "MISSING_CONVERSATION_ID",
            message: "Conversation ID is required",
          }),
        );
        return;
      }

      const deleted = await this.redisService.deleteConversationContext(conversationId);

      if (deleted) {
        res.json(successResponse({ message: `Cache cleared for conversation ${conversationId}` }));
      } else {
        res.status(404).json(
          errorResponse({
            code: "CONTEXT_NOT_FOUND",
            message: "Conversation context not found or already cleared",
          }),
        );
      }
    } catch (error) {
      logger.error("Error in deleteConversationCache controller:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "An internal error occurred while clearing cache",
        }),
      );
    }
  };
}
