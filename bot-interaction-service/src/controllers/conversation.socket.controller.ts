import { Socket } from "socket.io";
import { AppContext } from "../types/context.types";
import { SocketEvents } from "../constants/socket.events";
import { SocketService } from "../services/socket.service";
import { ConversationIdPayload, SocketMessagePayload } from "../schemas/conversation.schemas";
import { logger, successResponse, errorResponse } from "@neuratalk/common";
import { FlowEngine } from "../engine/flow-engine";
import { RedisService } from "../services/redis.service";
import { getConversationRoom } from "../constants/socket.rooms";

export class ConversationSocketController {
  private readonly socketService: SocketService;
  private readonly flowEngine: FlowEngine;
  private readonly redisService: RedisService;

  constructor(private readonly context: AppContext) {
    this.socketService = context.socketService;
    this.flowEngine = FlowEngine.getInstance(this.context);
    this.redisService = this.context.redis;
    logger.info("ConversationSocketController initialized.");
  }

  
  public handleJoinConversation = async (socket: Socket, conversationId: string): Promise<void> => {
    if(socket.rooms.has(getConversationRoom(conversationId))) {
      logger.info(`Client ${socket.id} already in conversation room: ${conversationId}`);
      return;
    }
    await socket.join(getConversationRoom(conversationId));
    logger.info(`Client ${socket.id} joined conversation room: ${conversationId}`);
    this.socketService.emit(conversationId, SocketEvents.STATUS, {
      message: `Joined conversation ${conversationId}`,
    });
  };

  
  public handleSendMessage = async (socket: Socket, data: SocketMessagePayload): Promise<void> => {
    const { conversationId, message } = data;
    await this.handleJoinConversation(socket, conversationId); // Ensure client is in the room
    logger.info(`Handling SEND_MESSAGE for conversation ${conversationId}: ${message.content}`);

    try {
      const result = await this.flowEngine.processMessage(conversationId, message, false);

      if (result.success) {
        this.socketService.emit(conversationId, SocketEvents.BOT_RESPONSE, {
          conversationId,
          response: result.messages,
        });
      } else {
        logger.error(`Flow execution failed for conversation ${conversationId}: ${result.error}`);
        this.socketService.emit(conversationId, SocketEvents.BOT_RESPONSE, {
          code: "FLOW_EXECUTION_ERROR",
          message: result.error || "Failed to process message",
        });
      }
    } catch (error) {
      logger.error(`Error in handleSendMessage for ${conversationId}:`, error);
      this.socketService.emit(conversationId, SocketEvents.BOT_RESPONSE, {
        code: "INTERNAL_ERROR",
        message: "An internal error occurred while processing your message.",
      });
    }
  };

  
  public handleSendPreviewMessage = async (
    socket: Socket,
    data: SocketMessagePayload,
  ): Promise<void> => {
    const { conversationId, message } = data;
    await this.handleJoinConversation(socket, conversationId); // Ensure client is in the room
    logger.info(
      `Handling SEND_PREVIEW_MESSAGE for conversation ${conversationId}: ${message.content}`,
    );

    try {
      const result = await this.flowEngine.processMessage(conversationId, message, true);

      if (result.success) {
        this.socketService.emit(conversationId, SocketEvents.PREVIEW_BOT_RESPONSE, {
          conversationId,
          response: result.messages,
        });
      } else {
        logger.error(
          `Preview flow execution failed for conversation ${conversationId}: ${result.error}`,
        );
        this.socketService.emit(conversationId, SocketEvents.PREVIEW_BOT_RESPONSE, {
          code: "FLOW_EXECUTION_ERROR",
          message: result.error || "Failed to process message in preview mode",
        });
      }
    } catch (error) {
      logger.error(`Error in handleSendPreviewMessage for ${conversationId}:`, error);
      this.socketService.emit(conversationId, SocketEvents.PREVIEW_BOT_RESPONSE, {
        code: "INTERNAL_ERROR",
        message: "An internal error occurred while processing your preview message.",
      });
    }
  };

  
  public handleDeleteConversationCache = async (data: ConversationIdPayload): Promise<void> => {
    const { conversationId } = data;
    logger.info(`Handling DELETE_CONVERSATION_CACHE for conversation ${conversationId}`);

    try {
      const deleted = await this.redisService.deleteConversationContext(conversationId);

      if (deleted) {
        this.socketService.emit(
          conversationId,
          SocketEvents.STATUS,
          successResponse({ message: `Cache cleared for conversation ${conversationId}` }),
        );
      } else {
        this.socketService.emit(
          conversationId,
          SocketEvents.DELETE_CONVERSATION_CACHE,
          errorResponse({
            code: "CONTEXT_NOT_FOUND",
            message: "Conversation context not found or already cleared",
          }),
        );
      }
    } catch (error) {
      logger.error(`Error in handleDeleteConversationCache for ${conversationId}:`, error);
      this.socketService.emit(conversationId, SocketEvents.DELETE_CONVERSATION_CACHE, {
        code: "INTERNAL_ERROR",
        message: "An internal error occurred while clearing cache",
      });
    }
  };
}