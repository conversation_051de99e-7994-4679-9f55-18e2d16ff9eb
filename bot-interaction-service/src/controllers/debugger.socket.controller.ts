import { Socket } from "socket.io";
import { AppContext } from "../types/context.types";
import { SocketEvents } from "../constants/socket.events";
import { SocketService } from "../services/socket.service";
import { DebuggerService } from "../services/debugger.service";
import { logger } from "@neuratalk/common";
import { ConversationIdPayload } from "../schemas/conversation.schemas";
import { getDebuggerRoom } from "../constants/socket.rooms";

export class DebuggerSocketController {
  private readonly socketService: SocketService;
  private readonly debuggerService: DebuggerService;

  constructor(private readonly context: AppContext) {
    this.socketService = context.socketService;
    this.debuggerService = this.context.debuggerService;
    logger.info("DebuggerSocketController initialized.");
  }

  public handleDebuggerSubscribe = (socket: Socket, data: ConversationIdPayload): void => {
    const { conversationId } = data;
    logger.info(`Client ${socket.id} subscribing to debugger for conversation ${conversationId}`);

    // Register this WebSocket client with the DebuggerService
    this.debuggerService.addClient(conversationId, socket);

    // Join a specific room for debugger events for this conversation
    void socket.join(getDebuggerRoom(conversationId));

    this.socketService.emit(conversationId, SocketEvents.STATUS, {
      message: `Subscribed to debugger for ${conversationId}`,
    });
  };

  public handleDebuggerUnsubscribe = (socket: Socket, data: ConversationIdPayload): void => {
    const { conversationId } = data;
    logger.info(
      `Client ${socket.id} unsubscribing from debugger for conversation ${conversationId}`,
    );

    this.debuggerService.removeClient(conversationId);
    void socket.leave(getDebuggerRoom(conversationId));

    this.socketService.emit(conversationId, SocketEvents.STATUS, {
      message: `Unsubscribed from debugger for ${conversationId}`,
    });
  };
}
