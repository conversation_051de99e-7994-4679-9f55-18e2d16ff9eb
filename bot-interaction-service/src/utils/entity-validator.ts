import { EntityMetadata, EntityType } from "@neuratalk/bot-store";

export function validateEntityValue(
  value: string,
  type: EntityType,
  metadata?: EntityMetadata,
): boolean {
  switch (type) {
    case EntityType.EMAIL:
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
    case EntityType.NUMBER:
      return !isNaN(Number(value));
    case EntityType.DATE:
      // Handle numeric timestamps (as strings)
      if (/^\d+$/.test(value)) {
        return !isNaN(new Date(Number(value)).getTime());
      }
      // Handle regular date strings
      return !isNaN(new Date(value).getTime());
    case EntityType.REGEX: {
      const regexPattern = metadata?.value;
      if (typeof regexPattern === "string" && regexPattern.length > 0) {
        try {
          const regex = new RegExp(regexPattern);
          return regex.test(value);
        } catch (e) {
          console.error("Invalid regex pattern:", regexPattern, e);
          return false;
        }
      }
      return false;
    }
    case EntityType.TEXT:
    default:
      return true;
  }
}
