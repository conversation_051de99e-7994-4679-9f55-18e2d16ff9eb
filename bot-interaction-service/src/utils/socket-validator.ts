import { Socket } from "socket.io";
import { ZodSchema, ZodError } from "zod";
import { SocketEvents } from "../constants/socket.events";
import { logger } from "@neuratalk/common";

export const validateSocketPayloadHOC =
  <T>(schema: ZodSchema<T>, socket: Socket, responseEvent: SocketEvents) =>
    (handler: (data: T) => Promise<void> | void) =>
      async (data: unknown): Promise<void> => {
        try {
          const parsedData = schema.parse(data);
          await handler(parsedData);
          socket.emit(responseEvent, {
            success: true
          });
        } catch (error) {
          if (error instanceof ZodError) {
            logger.warn(
              `Socket payload validation failed for ${socket.id} with event ${responseEvent}:`,
              error.errors,
            );

            socket.emit(responseEvent, {
              success: false,
              error: {
                code: "VALIDATION_ERROR",
                message: "Invalid payload format",
                details: error.errors.map((err) => ({
                  path: err.path.join("."),
                  message: err.message,
                })),
              },
            });
          } else {
            logger.error(
              `Unexpected error during socket payload validation for ${socket.id} with event ${responseEvent}:`,
              error,
            );

            socket.emit(responseEvent, {
              success: false,
              error: {
                code: "INTERNAL_ERROR",
                message: "Something went wrong",
              },
            });
          }
        }
      };
