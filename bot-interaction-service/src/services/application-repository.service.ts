import { RedisService } from "./redis.service";
import { DatabaseService } from "./database.service";
import { Entities, Flow, Bot, IntentItems, FaqItems, FaqTranslation } from "@neuratalk/bot-store";
import { logger } from "@neuratalk/common";

const DEFAULT_CACHE_TTL_SECONDS = 3600; // 1 hour

export class ApplicationRepository {
  constructor(
    private databaseService: DatabaseService,
    private redisService: RedisService,
  ) {}

  async getEntityByIdAndBotId(
    botId: string,
    entityId: string,
    forceRefresh: boolean = false,
  ): Promise<Entities | null> {
    const cacheKey = `entity:${botId}:${entityId}`;
    const ttlSeconds = DEFAULT_CACHE_TTL_SECONDS;

    return this.redisService.getOrSet(
      cacheKey,
      async () => {
        logger.debug(`Fetching entity ${entityId} for bot ${botId} from database.`);
        return this.databaseService.getEntityByIdAndBotId(botId, entityId);
      },
      ttlSeconds,
      forceRefresh,
    );
  }

  async getFlow(flowId: string, forceRefresh: boolean = false): Promise<Flow | null> {
    const cacheKey = `flow:${flowId}`;
    const ttlSeconds = DEFAULT_CACHE_TTL_SECONDS;

    return this.redisService.getOrSet(
      cacheKey,
      async () => {
        logger.debug(`Fetching flow ${flowId} from database.`);
        return this.databaseService.getFlow(flowId);
      },
      ttlSeconds,
      forceRefresh,
    );
  }

  async getFlowsByBot(botId: string, forceRefresh: boolean = false): Promise<Flow[]> {
    const cacheKey = `flows:bot:${botId}`;
    const ttlSeconds = DEFAULT_CACHE_TTL_SECONDS; // Cache for 1 hour

    return this.redisService.getOrSet(
      cacheKey,
      async () => {
        logger.debug(`Fetching flows for bot ${botId} from database.`);
        return this.databaseService.getFlowsByBot(botId);
      },
      ttlSeconds,
      forceRefresh,
    );
  }

  async getIntentItemByName(
    botId: string,
    name: string,
    forceRefresh: boolean = false,
  ): Promise<IntentItems | null> {
    const cacheKey = `intentItemByName:${botId}:${name}`;
    const ttlSeconds = DEFAULT_CACHE_TTL_SECONDS;

    return this.redisService.getOrSet(
      cacheKey,
      async () => {
        logger.debug(`Fetching intent item by name ${name} for bot ${botId} from database.`);
        return this.databaseService.getIntentItemByName(botId, name);
      },
      ttlSeconds,
      forceRefresh,
    );
  }

  async getIntentItemWithFlow(
    intentItemId: string,
    forceRefresh: boolean = false,
  ): Promise<IntentItems | null> {
    const cacheKey = `intentItemWithFlow:${intentItemId}`;
    const ttlSeconds = DEFAULT_CACHE_TTL_SECONDS;

    return this.redisService.getOrSet(
      cacheKey,
      async () => {
        logger.debug(`Fetching intent item ${intentItemId} with flow from database.`);
        return this.databaseService.getIntentItemWithFlow(intentItemId);
      },
      ttlSeconds,
      forceRefresh,
    );
  }

  async getFaqItemWithFlow(
    faqItemId: string,
    forceRefresh: boolean = false,
  ): Promise<FaqItems | null> {
    const cacheKey = `faqItemWithFlow:${faqItemId}`;
    const ttlSeconds = DEFAULT_CACHE_TTL_SECONDS;

    return this.redisService.getOrSet(
      cacheKey,
      async () => {
        logger.debug(`Fetching FAQ item ${faqItemId} with flow from database.`);
        return this.databaseService.getFaqItemWithFlow(faqItemId);
      },
      ttlSeconds,
      forceRefresh,
    );
  }

  async getBot(botId: string, forceRefresh: boolean = false): Promise<Bot | null> {
    const cacheKey = `bot:${botId}`;
    const ttlSeconds = DEFAULT_CACHE_TTL_SECONDS;

    return this.redisService.getOrSet(
      cacheKey,
      async () => {
        logger.debug(`Fetching bot ${botId} from database.`);
        return this.databaseService.getBot(botId, !forceRefresh);
      },
      ttlSeconds,
      forceRefresh,
    );
  }

  async getFaqTranslation(faqItemId: string, langCode: string, forceRefresh: boolean = false): Promise<FaqTranslation | null> {
    const cacheKey = `faq_translation:${faqItemId}:${langCode}`;
    const ttlSeconds = DEFAULT_CACHE_TTL_SECONDS;

    return this.redisService.getOrSet(
      cacheKey,
      async () => {
        logger.debug(`Fetching FAQ translation for ${faqItemId}:${langCode} from database.`);
        return this.databaseService.getFaqTranslation(faqItemId, langCode);
      },
      ttlSeconds,
      forceRefresh,
    );
  }
}
