import { Request, Response } from "express";
import { logger } from "@neuratalk/common";
import { Socket } from "socket.io";
import { SocketEvents } from "../constants/socket.events";
import {
  DebuggerEventType,
  LogType,
  NluLogPayload,
  ContextPayload,
  DebuggerEvent,
} from "../types/debugger.types";

export class DebuggerService {
  private static instance: DebuggerService;
  private clients: Map<string, Response | Socket> = new Map();

  private constructor() {
    logger.info("DebuggerService initialized as a singleton.");
  }

  public static getInstance(): DebuggerService {
    if (!DebuggerService.instance) {
      DebuggerService.instance = new DebuggerService();
    }
    return DebuggerService.instance;
  }

  public addClient(conversationId: string, client: Response | Socket, req?: Request): void {
    if (this.clients.has(conversationId)) {
      logger.debug(`Debugger client already exists for conversation ${conversationId}. `);
      return;
    }
    this.clients.set(conversationId, client);

    logger.debug(`Debugger client added for conversation ${conversationId}. `);

    if (client instanceof Socket) {
      client.on("disconnect", () => {
        this.removeClient(conversationId);
        logger.debug(`Debugger WebSocket client removed for conversation ${conversationId}. `);
      });
    } else if (req) {
      req.on("close", () => {
        this.removeClient(conversationId);
        logger.debug(`Debugger SSE client removed for conversation ${conversationId}. `);
      });
    } else {
      logger.warn(
        `DebuggerService: SSE client added without Request object for conversation ${conversationId}. Client disconnection might not be properly detected.`,
      );
      client.on("close", () => {
        this.removeClient(conversationId);
        logger.debug(`Debugger SSE client removed for conversation ${conversationId} (fallback). `);
      });
    }
  }

  public removeClient(conversationId: string): void {
    this.clients.delete(conversationId);
  }

  public emit(event: DebuggerEvent): void {
    const client = this.clients.get(event.conversationId);

    if (client) {
      if (client instanceof Socket) {
        try {
          client.emit(SocketEvents.DEBUGGER_EVENT, event);
        } catch (error) {
          logger.error(
            `Error emitting to debugger WebSocket client for conversation ${event.conversationId}:`,
            error,
          );
          this.removeClient(event.conversationId);
        }
      } else {
        const data = `data: ${JSON.stringify(event)}

`;
        try {
          client.write(data);
          client.flush();
        } catch (error) {
          logger.error(
            `Error writing to debugger SSE client for conversation ${event.conversationId}:`,
            error,
          );
          this.removeClient(event.conversationId);
        }
      }
    } else {
      logger.debug(
        `No debugger clients connected for conversation ${event.conversationId}, event emitted internally.`,
      );
    }
  }

  public log(level: LogType, message: string, conversationId: string, details?: any): void {
    this.emit({
      type: DebuggerEventType.LOG,
      timestamp: new Date().toISOString(),
      conversationId,
      payload: { level, message, details },
    });
  }

  public nluLog(nluDetails: NluLogPayload, conversationId: string): void {
    this.emit({
      type: DebuggerEventType.NLU_LOG,
      timestamp: new Date().toISOString(),
      conversationId,
      payload: nluDetails,
    });
  }

  public context(contextObject: ContextPayload, conversationId: string): void {
    this.emit({
      type: DebuggerEventType.CONTEXT,
      timestamp: new Date().toISOString(),
      conversationId,
      payload: contextObject,
    });
  }
}
