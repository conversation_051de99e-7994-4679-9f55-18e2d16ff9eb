import { Socket, Server as SocketIOServer } from "socket.io";
import { logger } from "@neuratalk/common";
import { SocketEvents } from "../constants/socket.events";
import { Server as HttpServer } from "http";
import { getConversationRoom } from "../constants/socket.rooms";

export class SocketService {
  public io: SocketIOServer;

  constructor(readonly httpServer: HttpServer) {
    this.io = new SocketIOServer(httpServer, {
      pingTimeout: 60000, // Disconnect after 60 seconds of no pong
    });

    this.setupConnectionHandlers();

    logger.info("SocketService initialized.");
  }

  private setupConnectionHandlers(): void {
    this.io.on(SocketEvents.CONNECTION, (socket: Socket) => {
      logger.info(`Client connected: ${socket.id}`);

      socket.on(SocketEvents.DISCONNECT, () => {
        logger.info(`Client disconnected: ${socket.id}`);
      });

      socket.on(SocketEvents.ERROR, (err: Error) => {
        logger.error(`Socket error for ${socket.id}:`, err);
      });
    });
  }

  /**
   * Emits a message to a specific conversation room.
   * @param conversationId The ID of the conversation room.
   * @param event The event name (e.g., "bot_response", "status").
   * @param data The data to send.
   */
  public emit(conversationId: string, event: SocketEvents, data: any): void {
    this.io.to(getConversationRoom(conversationId)).emit(event, data);
    logger.debug(
      `Emitted event "${event}" to room "${conversationId}" with data: ${JSON.stringify(data)}`,
    );
  }

  public async disconnect(): Promise<void> {
    this.io.disconnectSockets(true);
    await this.io.close();
    logger.info("Socket.IO server closed.");
  }
}
