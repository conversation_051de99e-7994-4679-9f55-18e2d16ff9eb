#!/bin/bash

# This script installs dependencies and builds the project modules in a specific order.

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Directories for npm install ---
INSTALL_DIRS=(
    "bot-builder-service"
    "bot-interaction-service"
    "packages/common"
    "packages/bot-store"
)

# --- Directories for npm run build (in order) ---
BUILD_DIRS=(
    "packages/common"
    "packages/bot-store"
    "studio/api_gw"
    "studio/app_engine"
)

# Get the root directory of the project (assuming the script is in the root)
ROOT_DIR=$(pwd)

echo "=================================================="
echo "Starting dependency installation..."
echo "=================================================="

for dir in "${INSTALL_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "\n>>> Installing dependencies in '$dir'..."
        (cd "$dir" && npm i)
    else
        echo "\n!!! Warning: Directory '$dir' not found. Skipping. !!!"
    fi
done


echo "\n=================================================="
echo "Starting build process..."
echo "=================================================="

for dir in "${BUILD_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "\n>>> Building '$dir'..."
        (cd "$dir" && npm run build)
    else
        echo "\n!!! Warning: Directory '$dir' not found. Skipping. !!!"
    fi
done

echo "\n=================================================="
echo "Script finished successfully."
echo "=================================================="
